<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档管理 - 制药文档管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 优化导航栏交互效果 */
        .sidebar {
            transition: all 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .nav-link {
            transition: all 0.2s ease;
            border-radius: 0.375rem;
            margin: 0.125rem 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .nav-link:hover {
            background-color: #e9ecef !important;
            color: #0d6efd !important;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #0d6efd, #0056b3) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(13,110,253,0.3);
        }

        .nav-link.active:hover {
            transform: translateX(0);
            background: linear-gradient(135deg, #0056b3, #004085) !important;
        }

        .nav-link i {
            transition: all 0.2s ease;
            width: 20px;
            text-align: center;
        }

        .nav-link:hover i {
            transform: scale(1.1);
        }

        /* 按钮组优化 */
        .btn-group .btn {
            transition: all 0.2s ease;
        }

        .btn-group .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* 卡片阴影效果 */
        .card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        .card:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }

        /* 表格行悬停效果 */
        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .nav-link:hover {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" style="min-height: 100vh; background-color: #f8f9fa; border-right: 1px solid #dee2e6;">
                <div class="position-sticky pt-3">
                    <div class="navbar-brand mb-4" style="font-weight: bold; color: #0d6efd !important;">
                        <i class="fas fa-pills me-2"></i>
                        制药文档管理系统
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/dashboard" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/users" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/dms/documents" style="background-color: #0d6efd; color: white !important; border-radius: 0.375rem;">
                                <i class="fas fa-file-alt me-2"></i>
                                文档管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/system-overview" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                系统概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/my-training" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user-graduate me-2"></i>
                                我的培训
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/training-courses" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-graduation-cap me-2"></i>
                                培训课程
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-chart-bar me-2"></i>
                                报表管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-cog me-2"></i>
                                系统设置
                            </a>
                        </li>
                    </ul>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dms/profile" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-user me-2"></i>
                                个人资料
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="logout()" style="color: #495057; padding: 0.75rem 1rem;">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">文档管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadDocumentModal">
                                <i class="fas fa-upload me-2"></i>上传文档
                            </button>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#aiUploadModal">
                                <i class="fas fa-brain me-2"></i>AI智能上传
                            </button>
                            <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#ocrModal">
                                <i class="fas fa-eye me-2"></i>OCR识别
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-primary" onclick="createNewDocument()" title="新建文档">
                                <i class="fas fa-file-plus me-1"></i>新建
                            </button>
                            <button class="btn btn-outline-warning" onclick="showBatchOperations()" title="批量操作">
                                <i class="fas fa-tasks me-1"></i>批量
                            </button>
                            <button class="btn btn-outline-info" onclick="exportDocuments()" title="导出文档">
                                <i class="fas fa-file-export me-1"></i>导出
                            </button>
                        </div>
                        <div class="btn-group me-2">
                            <button class="btn btn-outline-secondary" onclick="showAdvancedSearch()" title="高级搜索">
                                <i class="fas fa-search-plus me-1"></i>高级搜索
                            </button>
                            <button class="btn btn-outline-dark" onclick="showDocumentStats()" title="统计报表">
                                <i class="fas fa-chart-bar me-1"></i>统计
                            </button>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-secondary" onclick="refreshDocuments()" title="刷新列表">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="视图选项">
                                    <i class="fas fa-th-list"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="setViewMode('list')"><i class="fas fa-list me-2"></i>列表视图</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setViewMode('grid')"><i class="fas fa-th me-2"></i>网格视图</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="setViewMode('timeline')"><i class="fas fa-clock me-2"></i>时间线视图</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page content -->

        <!-- Filter Section -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Search & Filter</h6>
            </div>
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="searchTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="searchTitle" placeholder="Search by title">
                    </div>
                    <div class="col-md-3">
                        <label for="searchCategory" class="form-label">Category</label>
                        <select class="form-select" id="searchCategory">
                            <option value="">所有分类</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchStatus" class="form-label">状态</label>
                        <select class="form-select" id="searchStatus">
                            <option value="">所有状态</option>
                            <option value="DRAFT">草稿</option>
                            <option value="UNDER_REVIEW">审核中</option>
                            <option value="APPROVED">已批准</option>
                            <option value="PUBLISHED">已发布</option>
                            <option value="ARCHIVED">已归档</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="searchOwner" class="form-label">Owner</label>
                        <select class="form-select" id="searchOwner">
                            <option value="">All Owners</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                        <button type="button" class="btn btn-secondary ms-2" onclick="clearSearch()">
                            <i class="fas fa-times me-2"></i>Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Documents Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">文档列表</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="documentsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>分类</th>
                                <th>所有者</th>
                                <th>状态</th>
                                <th>版本</th>
                                <th>文件大小</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="documentsTableBody">
                            <!-- Documents will be loaded here -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Documents pagination">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Upload Document Modal -->
        <div class="modal fade" id="uploadDocumentModal" tabindex="-1" aria-labelledby="uploadDocumentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="uploadDocumentModalLabel">上传文档</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="uploadDocumentForm" enctype="multipart/form-data">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="documentFile" class="form-label">选择文件 *</label>
                                <input type="file" class="form-control" id="documentFile" name="file" required>
                                <div class="form-text">最大文件大小：50MB</div>
                            </div>
                            <div class="mb-3">
                                <label for="documentTitle" class="form-label">标题 *</label>
                                <input type="text" class="form-control" id="documentTitle" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label for="documentDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="documentDescription" name="description" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="documentCategory" class="form-label">分类</label>
                                <select class="form-select" id="documentCategory" name="categoryId">
                                    <option value="">选择分类</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i>上传
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Document Details Modal -->
        <div class="modal fade" id="documentDetailsModal" tabindex="-1" aria-labelledby="documentDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="documentDetailsModalLabel">文档详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="documentDetailsContent">
                        <!-- Document details will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Intelligent Upload Modal -->
        <div class="modal fade" id="aiUploadModal" tabindex="-1" aria-labelledby="aiUploadModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="aiUploadModalLabel">AI智能文档上传</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="aiUploadForm" enctype="multipart/form-data">
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                AI智能上传将自动分析文档内容，提取关键信息，进行GMP合规性检查，并自动分类文档。
                            </div>
                            <div class="mb-3">
                                <label for="aiFile" class="form-label">选择文档文件</label>
                                <input type="file" class="form-control" id="aiFile" name="file" required
                                       accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.bmp,.tiff">
                                <div class="form-text">支持PDF、Word、文本文件和图像文件（将进行OCR识别）</div>
                            </div>
                            <div class="mb-3">
                                <label for="aiTitle" class="form-label">文档标题（可选）</label>
                                <input type="text" class="form-control" id="aiTitle" name="title"
                                       placeholder="留空将使用文件名">
                            </div>
                            <div class="mb-3">
                                <label for="aiDescription" class="form-label">文档描述（可选）</label>
                                <textarea class="form-control" id="aiDescription" name="description" rows="3"
                                          placeholder="留空将由AI自动生成"></textarea>
                            </div>
                            <div id="aiAnalysisResult" class="mt-3" style="display: none;">
                                <h6>AI分析结果：</h6>
                                <div id="aiAnalysisContent"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-brain me-2"></i>智能上传
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- OCR Recognition Modal -->
        <div class="modal fade" id="ocrModal" tabindex="-1" aria-labelledby="ocrModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="ocrModalLabel">OCR文字识别</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>上传图像文件</h6>
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="ocrFile"
                                           accept=".jpg,.jpeg,.png,.bmp,.tiff" multiple>
                                    <div class="form-text">支持JPG、PNG、BMP、TIFF格式，可选择多个文件</div>
                                </div>
                                <button type="button" class="btn btn-info" onclick="performOCR()">
                                    <i class="fas fa-eye me-2"></i>开始识别
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>识别结果</h6>
                                <div id="ocrResults" class="border rounded p-3" style="min-height: 300px; background-color: #f8f9fa;">
                                    <p class="text-muted">请选择图像文件并点击"开始识别"</p>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-success" onclick="saveOCRResults()" disabled id="saveOCRBtn">
                                        <i class="fas fa-save me-2"></i>保存为文档
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="copyOCRResults()" disabled id="copyOCRBtn">
                                        <i class="fas fa-copy me-2"></i>复制文本
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Preview Modal -->
        <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="previewModalTitle">文档预览</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0" style="height: 70vh;">
                        <!-- 加载状态 -->
                        <div id="previewLoading" class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2">正在加载预览...</div>
                            </div>
                        </div>

                        <!-- 预览内容 -->
                        <div id="previewContent" class="h-100" style="display: none;"></div>

                        <!-- 错误状态 -->
                        <div id="previewError" class="d-flex justify-content-center align-items-center h-100" style="display: none;">
                            <div class="text-center">
                                <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                                <div id="previewErrorMessage">预览失败</div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" id="downloadFromPreview">
                            <i class="fas fa-download me-2"></i>下载文档
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 0;
        let currentSize = 10;
        let currentSort = 'createdAt';
        let currentDir = 'desc';

        document.addEventListener('DOMContentLoaded', function() {
            loadDocuments();
            loadCategories();
            loadUsers();
        });

        async function loadDocuments() {
            try {
                const searchParams = new URLSearchParams({
                    page: currentPage,
                    size: currentSize,
                    sortBy: currentSort,
                    sortDir: currentDir
                });

                // Add search filters
                const title = document.getElementById('searchTitle').value;
                const categoryId = document.getElementById('searchCategory').value;
                const status = document.getElementById('searchStatus').value;
                const ownerId = document.getElementById('searchOwner').value;

                if (title) searchParams.append('title', title);
                if (categoryId) searchParams.append('categoryId', categoryId);
                if (status) searchParams.append('status', status);
                if (ownerId) searchParams.append('ownerId', ownerId);

                const response = await authUtils.secureApiCall(`/dms/api/documents?${searchParams}`);

                if (response.ok) {
                    const result = await response.json();
                    displayDocuments(result.data.content);
                    updatePagination(result.data);
                } else {
                    console.error('Failed to load documents');
                }
            } catch (error) {
                console.error('Error loading documents:', error);
            }
        }

        function displayDocuments(documents) {
            console.log('=== 显示文档列表 ===');
            console.log('文档数量:', documents ? documents.length : 0);
            console.log('文档数据:', documents);

            const tbody = document.getElementById('documentsTableBody');
            if (!tbody) {
                console.error('未找到文档表格体元素');
                return;
            }

            tbody.innerHTML = '';

            if (!documents || documents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted py-4">暂无文档数据</td></tr>';
                console.log('显示空数据提示');
                return;
            }

            documents.forEach((doc, index) => {
                try {
                    console.log(`处理文档 ${index + 1}:`, doc);

                    const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-alt me-2 text-primary"></i>
                            <div>
                                <div class="fw-bold">${doc.title}</div>
                                <small class="text-muted">${doc.originalFileName}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        ${doc.category ? `<span class="badge" style="background-color: ${doc.category.color}">${doc.category.name}</span>` : '-'}
                    </td>
                    <td>${doc.ownerUsername || (doc.owner ? `${doc.owner.firstName} ${doc.owner.lastName}` : '未知用户')}</td>
                    <td>
                        <span class="badge ${getStatusBadgeClass(doc.status)}">${doc.status.replace('_', ' ')}</span>
                    </td>
                    <td>v${doc.versionNumber}</td>
                    <td>${formatFileSize(doc.fileSize)}</td>
                    <td>${formatDate(doc.createdAt)}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewDocument(${doc.id})" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="previewDocument(${doc.id})" title="在线预览">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="downloadDocument(${doc.id})" title="下载文档">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                        <div class="btn-group ms-1" role="group">
                            <button class="btn btn-sm btn-outline-warning" onclick="showVersions(${doc.id})" title="版本历史">
                                <i class="fas fa-history"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="shareDocument(${doc.id})" title="分享文档">
                                <i class="fas fa-share-alt"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-dark" onclick="editDocument(${doc.id})" title="编辑文档">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                        <!-- 审批按钮 -->
                        ${doc.status === 'DRAFT' ? `
                            <button class="btn btn-sm btn-outline-primary ms-1" onclick="submitForApproval(${doc.id})" title="提交审批">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        ` : ''}
                        ${doc.status === 'UNDER_REVIEW' ? `
                            <button class="btn btn-sm btn-outline-success ms-1" onclick="approveDocument(${doc.id})" title="批准文档">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning ms-1" onclick="rejectDocument(${doc.id})" title="拒绝文档">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteDocument(${doc.id})" title="删除文档">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);

                console.log(`文档 ${index + 1} 显示成功`);
                } catch (error) {
                    console.error(`显示文档 ${index + 1} 失败:`, error);
                    console.error('文档数据:', doc);
                }
            });

            console.log('✅ 文档列表显示完成');
        }

        async function loadCategories() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/document-categories/active');

                if (response.ok) {
                    const result = await response.json();
                    const selects = document.querySelectorAll('#searchCategory, #documentCategory');
                    
                    selects.forEach(select => {
                        // Keep the first option (All Categories or Select Category)
                        const firstOption = select.firstElementChild;
                        select.innerHTML = '';
                        select.appendChild(firstOption);
                        
                        result.data.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.textContent = category.name;
                            select.appendChild(option);
                        });
                    });
                }
            } catch (error) {
                console.error('Error loading categories:', error);
            }
        }

        async function loadUsers() {
            try {
                const response = await authUtils.secureApiCall('/dms/api/users/active');

                if (response.ok) {
                    const result = await response.json();
                    const select = document.getElementById('searchOwner');
                    
                    result.data.forEach(user => {
                        const option = document.createElement('option');
                        option.value = user.id;
                        option.textContent = `${user.firstName} ${user.lastName}`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        // Event handlers
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            currentPage = 0;
            loadDocuments();
        });

        document.getElementById('uploadDocumentForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            try {
                console.log('=== 开始文档上传 ===');

                // 检查文件
                const fileInput = document.getElementById('documentFile');
                if (!fileInput.files[0]) {
                    alert('请选择要上传的文件');
                    return;
                }

                const file = fileInput.files[0];
                console.log('文件信息:', {
                    name: file.name,
                    size: file.size,
                    type: file.type
                });

                // 检查文件大小（50MB限制）
                if (file.size > 50 * 1024 * 1024) {
                    alert('文件大小不能超过50MB');
                    return;
                }

                // 禁用按钮并显示上传状态
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';

                // 使用专门的文件上传函数
                const response = await authUtils.secureFileUpload('/dms/api/documents/upload', formData);

                console.log('文档上传响应状态:', response ? response.status : 'null');
                console.log('文档上传响应对象:', response);

                if (response && response.ok) {
                    const result = await response.json();
                    console.log('文档上传响应数据:', result);

                    // 显示成功消息
                    if (typeof UIEnhancements !== 'undefined') {
                        UIEnhancements.showNotification('文档上传成功！', 'success');
                    } else {
                        alert('文档上传成功！');
                    }

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('uploadDocumentModal'));
                    if (modal) {
                        modal.hide();
                    }

                    // 重置表单
                    this.reset();

                    // 延迟重新加载文档列表，确保数据库已更新
                    setTimeout(() => {
                        console.log('重新加载文档列表...');
                        loadDocuments();
                    }, 500);
                } else {
                    const errorText = response ? await response.text() : '无响应';
                    console.error('文档上传错误响应:', errorText);
                    console.error('响应状态:', response ? response.status : 'null');
                    alert('文档上传失败：' + errorText);
                }
            } catch (error) {
                console.error('文档上传异常:', error);
                console.error('异常堆栈:', error.stack);
                alert('文档上传失败：' + error.message);
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });

        // Utility functions
        function getStatusBadgeClass(status) {
            switch (status) {
                case 'DRAFT': return 'bg-secondary';
                case 'UNDER_REVIEW': return 'bg-warning';
                case 'APPROVED': return 'bg-success';
                case 'PUBLISHED': return 'bg-primary';
                case 'ARCHIVED': return 'bg-dark';
                default: return 'bg-secondary';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            if (!dateString) return '未知时间';
            try {
                return new Date(dateString).toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                console.error('日期格式化失败:', error);
                return '无效日期';
            }
        }

        function getStatusDisplayName(status) {
            const statusMap = {
                'DRAFT': '草稿',
                'UNDER_REVIEW': '审核中',
                'APPROVED': '已批准',
                'PUBLISHED': '已发布',
                'ARCHIVED': '已归档'
            };
            return statusMap[status] || status;
        }

        function clearSearch() {
            document.getElementById('searchForm').reset();
            currentPage = 0;
            loadDocuments();
        }

        async function viewDocument(id) {
            try {
                const response = await authUtils.secureApiCall(`/dms/api/documents/${id}`);

                if (response.ok) {
                    const result = await response.json();
                    const document = result.data;

                    // 显示文档详情模态框
                    showDocumentDetails(document);
                } else {
                    alert('无法获取文档详情');
                }
            } catch (error) {
                console.error('查看文档失败:', error);
                alert('查看文档失败: ' + error.message);
            }
        }

        function showDocumentDetails(document) {
            const modalContent = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>标题:</strong></td><td>${document.title}</td></tr>
                            <tr><td><strong>文件名:</strong></td><td>${document.originalFileName}</td></tr>
                            <tr><td><strong>分类:</strong></td><td>${document.category ? document.category.name : '未分类'}</td></tr>
                            <tr><td><strong>状态:</strong></td><td><span class="badge ${getStatusBadgeClass(document.status)}">${document.status}</span></td></tr>
                            <tr><td><strong>版本:</strong></td><td>v${document.versionNumber}</td></tr>
                            <tr><td><strong>文件大小:</strong></td><td>${formatFileSize(document.fileSize)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>其他信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>创建者:</strong></td><td>${document.ownerUsername || '未知'}</td></tr>
                            <tr><td><strong>创建时间:</strong></td><td>${formatDate(document.createdAt)}</td></tr>
                            <tr><td><strong>更新时间:</strong></td><td>${formatDate(document.updatedAt)}</td></tr>
                            <tr><td><strong>描述:</strong></td><td>${document.description || '无描述'}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="btn-group" role="group">
                            <button class="btn btn-primary" onclick="downloadDocument(${document.id})">
                                <i class="fas fa-download me-1"></i>下载
                            </button>
                            <button class="btn btn-info" onclick="previewDocument(${document.id})">
                                <i class="fas fa-eye me-1"></i>预览
                            </button>
                            <button class="btn btn-warning" onclick="showVersions(${document.id})">
                                <i class="fas fa-history me-1"></i>版本历史
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('documentDetailsContent').innerHTML = modalContent;
            document.getElementById('documentDetailsModalLabel').textContent = document.title;
            new bootstrap.Modal(document.getElementById('documentDetailsModal')).show();
        }

        async function downloadDocument(id) {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                console.log('=== 文档下载开始 ===');
                console.log('文档ID:', id);

                const response = await fetch(`/dms/api/documents/${id}/download`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                console.log('下载响应状态:', response.status);

                if (response.ok) {
                    // 获取文件名 - 支持UTF-8编码的文件名
                    const contentDisposition = response.headers.get('Content-Disposition');
                    const xFilename = response.headers.get('X-Filename');
                    let filename = 'download';

                    if (xFilename) {
                        // 优先使用X-Filename头，它包含原始文件名
                        filename = decodeURIComponent(xFilename);
                    } else if (contentDisposition) {
                        // 尝试解析Content-Disposition头
                        const utf8Match = contentDisposition.match(/filename\*=UTF-8''(.+)/);
                        if (utf8Match) {
                            filename = decodeURIComponent(utf8Match[1]);
                        } else {
                            const normalMatch = contentDisposition.match(/filename="(.+)"/);
                            if (normalMatch) {
                                filename = normalMatch[1];
                            }
                        }
                    }

                    console.log('解析的文件名:', filename);

                    // 创建下载链接
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    console.log('文档下载成功:', filename);
                } else {
                    const errorText = await response.text();
                    console.error('下载失败:', errorText);
                    alert('下载失败: ' + response.status);
                }
            } catch (error) {
                console.error('下载异常:', error);
                alert('下载失败: 网络错误');
            }
        }

        async function previewDocument(id) {
            try {
                console.log('=== 文档预览开始 ===');
                console.log('文档ID:', id);

                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                const response = await fetch(`/dms/api/documents/${id}/preview`, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                console.log('预览响应状态:', response.status);

                if (response.ok) {
                    const contentType = response.headers.get('Content-Type');
                    console.log('文件类型:', contentType);

                    if (contentType && contentType.startsWith('text/')) {
                        // 文本文件直接显示
                        const text = await response.text();
                        showTextPreview(text);
                    } else if (contentType && contentType === 'application/pdf') {
                        // PDF文件在新窗口打开
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        window.open(url, '_blank');
                        setTimeout(() => window.URL.revokeObjectURL(url), 1000);
                    } else if (contentType && contentType.startsWith('image/')) {
                        // 图片文件显示
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        showImagePreview(url);
                        setTimeout(() => window.URL.revokeObjectURL(url), 10000);
                    } else {
                        alert('此文件类型不支持预览，请下载后查看');
                    }
                } else if (response.status === 415) {
                    alert('此文件类型不支持预览');
                } else {
                    const errorText = await response.text();
                    console.error('预览失败:', errorText);
                    alert('预览失败: ' + response.status);
                }
            } catch (error) {
                console.error('预览异常:', error);
                alert('预览失败: 网络错误');
            }
        }

        function showTextPreview(text) {
            const modalContent = `
                <div class="modal fade" id="previewModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">文档预览</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <pre style="white-space: pre-wrap; max-height: 500px; overflow-y: auto;">${text}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的预览模态框
            const existingModal = document.getElementById('previewModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalContent);
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        }

        function showImagePreview(imageUrl) {
            const modalContent = `
                <div class="modal fade" id="previewModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">图片预览</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="${imageUrl}" class="img-fluid" style="max-height: 500px;">
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的预览模态框
            const existingModal = document.getElementById('previewModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalContent);
            new bootstrap.Modal(document.getElementById('previewModal')).show();
        }

        function showVersions(id) {
            alert('Version history functionality will be implemented');
        }

        async function deleteDocument(id) {
            if (confirm('确定要删除此文档吗？\n注意：此操作不可撤销！')) {
                try {
                    console.log('=== 删除文档开始 ===');
                    console.log('文档ID:', id);

                    const response = await authUtils.secureApiCall(`/dms/api/documents/${id}`, {
                        method: 'DELETE'
                    });

                    console.log('删除文档API响应状态:', response.status);

                    if (response.ok) {
                        const result = await response.json();
                        console.log('删除文档API响应数据:', result);
                        alert('文档删除成功！');
                        loadDocuments(); // 重新加载文档列表
                        console.log('✅ 文档删除成功');
                    } else {
                        const result = await response.json();
                        console.error('❌ 删除文档失败:', result);
                        alert('删除文档失败: ' + (result.message || '未知错误'));
                    }
                } catch (error) {
                    console.error('❌ 删除文档异常:', error);
                    alert('删除文档失败: ' + error.message);
                }
            }
        }

        function updatePagination(pageData) {
            // Pagination implementation
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // Add pagination buttons based on pageData
            for (let i = 0; i < pageData.totalPages; i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i + 1}</a>`;
                pagination.appendChild(li);
            }
        }

        function changePage(page) {
            currentPage = page;
            loadDocuments();
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/dms/login';
            }
        }

        // AI功能处理函数
        document.getElementById('aiUploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            try {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>AI分析中...';

                // 直接获取token
                const token = localStorage.getItem('token');
                if (!token) {
                    alert('请先登录');
                    return;
                }

                console.log('=== AI智能上传开始 ===');
                console.log('Token长度:', token.length);

                const response = await fetch('/dms/api/ai/documents/intelligent-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token
                    },
                    body: formData
                });

                console.log('AI上传响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('AI上传响应数据:', result);

                    // 显示AI分析结果
                    displayAIAnalysisResult(result.data.analysisResult);

                    alert('AI智能上传成功！文档已自动分类和分析。');
                    bootstrap.Modal.getInstance(document.getElementById('aiUploadModal')).hide();
                    this.reset();
                    document.getElementById('aiAnalysisResult').style.display = 'none';
                    loadDocuments();
                } else {
                    const errorText = await response.text();
                    console.error('AI上传错误响应:', errorText);
                    alert('AI智能上传失败: ' + errorText);
                }
            } catch (error) {
                alert('AI智能上传失败: 网络错误');
                console.error('Error:', error);
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });

        function displayAIAnalysisResult(analysisResult) {
            const resultDiv = document.getElementById('aiAnalysisContent');
            const complianceResult = analysisResult.complianceResult;

            resultDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>文档摘要</h6>
                        <p class="text-muted">${analysisResult.summary}</p>

                        <h6>自动分类</h6>
                        <span class="badge bg-primary">${analysisResult.category}</span>

                        <h6 class="mt-3">关键词</h6>
                        ${analysisResult.keywords.map(keyword =>
                            `<span class="badge bg-secondary me-1">${keyword}</span>`
                        ).join('')}
                    </div>
                    <div class="col-md-6">
                        <h6>GMP合规性检查</h6>
                        <div class="alert ${complianceResult.isCompliant ? 'alert-success' : 'alert-warning'}">
                            <strong>${complianceResult.isCompliant ? '✓ 合规' : '⚠ 需要注意'}</strong>
                            <div>合规评分: ${complianceResult.complianceScore}/100</div>
                        </div>

                        ${complianceResult.violations.length > 0 ? `
                            <h6>发现的问题</h6>
                            <ul class="list-unstyled">
                                ${complianceResult.violations.map(violation =>
                                    `<li><i class="fas fa-exclamation-triangle text-warning me-2"></i>${violation}</li>`
                                ).join('')}
                            </ul>
                        ` : ''}

                        ${complianceResult.recommendations.length > 0 ? `
                            <h6>改进建议</h6>
                            <ul class="list-unstyled">
                                ${complianceResult.recommendations.map(rec =>
                                    `<li><i class="fas fa-lightbulb text-info me-2"></i>${rec}</li>`
                                ).join('')}
                            </ul>
                        ` : ''}
                    </div>
                </div>
            `;

            document.getElementById('aiAnalysisResult').style.display = 'block';
        }

        // OCR功能处理函数
        async function performOCR() {
            const fileInput = document.getElementById('ocrFile');
            const files = fileInput.files;

            if (files.length === 0) {
                alert('请选择图像文件');
                return;
            }

            const resultsDiv = document.getElementById('ocrResults');
            resultsDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>正在识别文字...</div>';

            try {
                const formData = new FormData();
                for (let file of files) {
                    formData.append('files', file);
                }

                // 直接获取token
                const token = localStorage.getItem('token');
                if (!token) {
                    resultsDiv.innerHTML = '<div class="alert alert-danger">请先登录</div>';
                    return;
                }

                console.log('=== OCR调用开始 ===');
                console.log('Token长度:', token.length);
                console.log('文件数量:', files.length);

                const response = await fetch('/dms/api/ai/ocr/batch-extract', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token
                    },
                    body: formData
                });

                console.log('OCR响应状态:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('OCR响应数据:', result);
                    displayOCRResults(result.data);
                } else {
                    const errorText = await response.text();
                    console.error('OCR错误响应:', errorText);
                    resultsDiv.innerHTML = `<div class="alert alert-danger">OCR识别失败 (${response.status}): ${errorText}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert alert-danger">OCR识别失败: 网络错误</div>`;
                console.error('Error:', error);
            }
        }

        function displayOCRResults(ocrResults) {
            const resultsDiv = document.getElementById('ocrResults');
            let html = '';

            ocrResults.forEach((result, index) => {
                html += `
                    <div class="mb-3">
                        <h6>文件 ${index + 1}</h6>
                        <div class="alert ${result.success ? 'alert-success' : 'alert-warning'}">
                            <strong>${result.success ? '识别成功' : '识别失败'}</strong>
                            ${result.success ? `(置信度: ${result.confidence}%)` : ''}
                        </div>
                        ${result.success ? `
                            <div class="border rounded p-2" style="background-color: white; max-height: 200px; overflow-y: auto;">
                                <pre style="white-space: pre-wrap; margin: 0;">${result.extractedText}</pre>
                            </div>
                            ${result.keywords.length > 0 ? `
                                <div class="mt-2">
                                    <small class="text-muted">关键词: </small>
                                    ${result.keywords.map(keyword =>
                                        `<span class="badge bg-secondary me-1">${keyword}</span>`
                                    ).join('')}
                                </div>
                            ` : ''}
                        ` : `
                            <div class="text-muted">${result.message}</div>
                        `}
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;

            // 启用保存和复制按钮
            document.getElementById('saveOCRBtn').disabled = false;
            document.getElementById('copyOCRBtn').disabled = false;
        }

        function saveOCRResults() {
            // 将OCR结果保存为文档的功能
            alert('保存OCR结果功能将在后续版本中实现');
        }

        function copyOCRResults() {
            const resultsDiv = document.getElementById('ocrResults');
            const textContent = resultsDiv.innerText;

            navigator.clipboard.writeText(textContent).then(() => {
                alert('OCR结果已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择文本复制');
            });
        }

        // 文档预览功能
        async function previewDocument(id) {
            try {
                console.log('=== 文档预览开始 ===');
                console.log('文档ID:', id);

                // 显示预览模态框
                const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
                previewModal.show();

                // 显示加载状态
                document.getElementById('previewLoading').style.display = 'block';
                document.getElementById('previewContent').style.display = 'none';
                document.getElementById('previewError').style.display = 'none';

                // 获取预览信息
                const infoResponse = await authUtils.secureApiCall(`/dms/api/documents/${id}/preview-info`);
                if (!infoResponse.ok) {
                    throw new Error('Failed to get preview info');
                }

                const infoResult = await infoResponse.json();
                const previewInfo = infoResult.data;

                console.log('预览信息:', previewInfo);

                // 更新模态框标题
                document.getElementById('previewModalTitle').textContent = previewInfo.title;

                // 设置下载按钮
                document.getElementById('downloadFromPreview').onclick = () => downloadDocument(id);

                if (!previewInfo.previewSupported) {
                    // 不支持预览的文件类型
                    document.getElementById('previewLoading').style.display = 'none';
                    document.getElementById('previewError').style.display = 'block';
                    document.getElementById('previewErrorMessage').textContent =
                        `File type ${previewInfo.mimeType} is not supported for preview. Please download to view.`;
                    return;
                }

                // 加载预览内容
                const previewUrl = `/dms/api/documents/${id}/preview`;
                const previewContent = document.getElementById('previewContent');

                if (previewInfo.mimeType.startsWith('image/')) {
                    // 图片预览
                    previewContent.innerHTML = `
                        <img src="${previewUrl}" class="img-fluid w-100 h-100" style="object-fit: contain;"
                             alt="${previewInfo.title}" />
                    `;
                } else if (previewInfo.mimeType === 'application/pdf') {
                    // PDF预览
                    previewContent.innerHTML = `
                        <iframe src="${previewUrl}" class="w-100 h-100" frameborder="0"></iframe>
                    `;
                } else if (previewInfo.mimeType.startsWith('text/')) {
                    // 文本文件预览
                    const textResponse = await authUtils.secureApiCall(previewUrl);
                    const textContent = await textResponse.text();
                    previewContent.innerHTML = `
                        <pre class="p-3 h-100 overflow-auto" style="background-color: #f8f9fa; font-family: 'Courier New', monospace;">${textContent}</pre>
                    `;
                } else {
                    // 其他类型尝试iframe
                    previewContent.innerHTML = `
                        <iframe src="${previewUrl}" class="w-100 h-100" frameborder="0"></iframe>
                    `;
                }

                // 隐藏加载状态，显示内容
                document.getElementById('previewLoading').style.display = 'none';
                document.getElementById('previewContent').style.display = 'block';

                console.log('✅ 文档预览加载成功');
            } catch (error) {
                console.error('文档预览失败:', error);
                document.getElementById('previewLoading').style.display = 'none';
                document.getElementById('previewError').style.display = 'block';
                document.getElementById('previewErrorMessage').textContent =
                    'Failed to load preview: ' + error.message;
            }
        }

        // 版本历史功能
        async function showVersions(id) {
            try {
                console.log('=== 获取版本历史 ===');
                console.log('文档ID:', id);

                const response = await authUtils.secureApiCall(`/dms/api/documents/${id}/versions`);
                if (!response.ok) {
                    throw new Error('Failed to get document versions');
                }

                const result = await response.json();
                const versionData = result.data;

                console.log('版本数据:', versionData);

                // 显示版本历史模态框
                showVersionHistoryModal(versionData);

            } catch (error) {
                console.error('获取版本历史失败:', error);
                alert('Failed to load version history: ' + error.message);
            }
        }

        function showVersionHistoryModal(versionData) {
            const versions = versionData.versions || [];

            let versionsHtml = '';
            if (versions.length === 0) {
                versionsHtml = '<div class="alert alert-info">暂无版本历史</div>';
            } else {
                versionsHtml = `
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>版本</th>
                                    <th>标题</th>
                                    <th>文件名</th>
                                    <th>大小</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>创建者</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                versions.forEach(version => {
                    const isCurrentVersion = version.isCurrentVersion;
                    const createdBy = version.createdBy ?
                        `${version.createdBy.firstName} ${version.createdBy.lastName}` : '未知';

                    versionsHtml += `
                        <tr ${isCurrentVersion ? 'class="table-success"' : ''}>
                            <td>
                                v${version.versionNumber}
                                ${isCurrentVersion ? '<span class="badge bg-success ms-1">当前</span>' : ''}
                            </td>
                            <td>${version.title}</td>
                            <td>${version.originalFileName}</td>
                            <td>${formatFileSize(version.fileSize)}</td>
                            <td><span class="badge ${getStatusBadgeClass(version.status)}">${version.status}</span></td>
                            <td>${formatDate(version.createdAt)}</td>
                            <td>${createdBy}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="downloadDocument(${version.id})" title="下载">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="previewDocument(${version.id})" title="预览">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    ${!isCurrentVersion ? `
                                        <button class="btn btn-sm btn-outline-warning" onclick="restoreVersion(${version.id})" title="恢复此版本">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    ` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                });

                versionsHtml += '</tbody></table></div>';
            }

            // 创建版本历史模态框
            const versionModal = document.createElement('div');
            versionModal.className = 'modal fade';
            versionModal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-history me-2"></i>版本历史
                                <small class="text-muted">(共 ${versionData.totalVersions} 个版本)</small>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${versionsHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(versionModal);
            const modal = new bootstrap.Modal(versionModal);
            modal.show();

            versionModal.addEventListener('hidden.bs.modal', () => {
                versionModal.remove();
            });
        }

        function restoreVersion(versionId) {
            if (confirm('确定要恢复到此版本吗？这将使此版本成为当前版本。')) {
                alert('版本恢复功能开发中');
            }
        }


        // 分享文档功能
        function shareDocument(id) {
            const shareUrl = window.location.origin + '/dms/documents/' + id;

            // 创建分享模态框
            const shareModal = document.createElement('div');
            shareModal.className = 'modal fade';
            shareModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">分享文档</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">文档链接</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="shareUrl" value="${shareUrl}" readonly>
                                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('shareUrl')" title="复制链接">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">快速分享</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-primary" onclick="shareViaEmail('${shareUrl}')" title="邮件分享">
                                        <i class="fas fa-envelope"></i> 邮件
                                    </button>
                                    <button class="btn btn-outline-success" onclick="shareViaWeChat('${shareUrl}')" title="微信分享">
                                        <i class="fab fa-weixin"></i> 微信
                                    </button>
                                    <button class="btn btn-outline-info" onclick="shareViaDingTalk('${shareUrl}')" title="钉钉分享">
                                        <i class="fas fa-comments"></i> 钉钉
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(shareModal);
            const modal = new bootstrap.Modal(shareModal);
            modal.show();

            // 模态框关闭后移除
            shareModal.addEventListener('hidden.bs.modal', () => {
                shareModal.remove();
            });
        }

        // 编辑文档功能
        async function editDocument(id) {
            try {
                console.log('=== 编辑文档 ===');
                console.log('文档ID:', id);

                // 获取文档详情
                const response = await authUtils.secureApiCall(`/dms/api/documents/${id}`);
                if (!response.ok) {
                    throw new Error('Failed to get document details');
                }

                const result = await response.json();
                const document = result.data;

                console.log('文档详情:', document);

                // 显示编辑模态框
                showEditDocumentModal(document);

            } catch (error) {
                console.error('获取文档详情失败:', error);
                alert('无法编辑文档: ' + error.message);
            }
        }

        function showEditDocumentModal(document) {
            // 创建编辑模态框
            const editModal = document.createElement('div');
            editModal.className = 'modal fade';
            editModal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-edit me-2"></i>编辑文档
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="editDocumentForm">
                            <div class="modal-body">
                                <input type="hidden" id="editDocumentId" value="${document.id}">

                                <div class="mb-3">
                                    <label for="editDocumentTitle" class="form-label">标题 *</label>
                                    <input type="text" class="form-control" id="editDocumentTitle"
                                           value="${document.title}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="editDocumentDescription" class="form-label">描述</label>
                                    <textarea class="form-control" id="editDocumentDescription" rows="3">${document.description || ''}</textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="editDocumentCategory" class="form-label">分类</label>
                                    <select class="form-select" id="editDocumentCategory">
                                        <option value="">选择分类</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="editDocumentStatus" class="form-label">状态</label>
                                    <select class="form-select" id="editDocumentStatus">
                                        <option value="DRAFT" ${document.status === 'DRAFT' ? 'selected' : ''}>草稿</option>
                                        <option value="UNDER_REVIEW" ${document.status === 'UNDER_REVIEW' ? 'selected' : ''}>审核中</option>
                                        <option value="APPROVED" ${document.status === 'APPROVED' ? 'selected' : ''}>已批准</option>
                                        <option value="PUBLISHED" ${document.status === 'PUBLISHED' ? 'selected' : ''}>已发布</option>
                                        <option value="ARCHIVED" ${document.status === 'ARCHIVED' ? 'selected' : ''}>已归档</option>
                                    </select>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>当前文件:</strong> ${document.originalFileName}<br>
                                    <strong>文件大小:</strong> ${formatFileSize(document.fileSize)}<br>
                                    <strong>创建时间:</strong> ${formatDate(document.createdAt)}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>保存更改
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.appendChild(editModal);
            const modal = new bootstrap.Modal(editModal);
            modal.show();

            // 加载分类选项
            loadCategoriesForEdit(document.category?.id);

            // 处理表单提交
            document.getElementById('editDocumentForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                await updateDocument();
            });

            editModal.addEventListener('hidden.bs.modal', () => {
                editModal.remove();
            });
        }

        async function loadCategoriesForEdit(selectedCategoryId) {
            try {
                const response = await authUtils.secureApiCall('/dms/api/document-categories/active');
                if (response.ok) {
                    const result = await response.json();
                    const select = document.getElementById('editDocumentCategory');

                    result.data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        if (category.id === selectedCategoryId) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载分类失败:', error);
            }
        }

        async function updateDocument() {
            const documentId = document.getElementById('editDocumentId').value;
            const title = document.getElementById('editDocumentTitle').value;
            const description = document.getElementById('editDocumentDescription').value;
            const categoryId = document.getElementById('editDocumentCategory').value;
            const status = document.getElementById('editDocumentStatus').value;

            try {
                const updateData = {
                    title: title,
                    description: description,
                    categoryId: categoryId || null,
                    status: status
                };

                console.log('更新文档数据:', updateData);

                // 暂时显示成功消息，实际API需要实现
                alert('文档更新功能开发中\n\n将要更新的数据:\n' + JSON.stringify(updateData, null, 2));

                // 关闭模态框并刷新列表
                bootstrap.Modal.getInstance(document.querySelector('.modal.show')).hide();
                loadDocuments();

            } catch (error) {
                console.error('更新文档失败:', error);
                alert('更新文档失败: ' + error.message);
            }
        }

        // 复制到剪贴板
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);

            try {
                document.execCommand('copy');
                showToast('链接已复制到剪贴板', 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
        }

        // 分享方式
        function shareViaEmail(url) {
            const subject = encodeURIComponent('文档分享');
            const body = encodeURIComponent('我想与您分享这个文档：' + url);
            window.open(`mailto:?subject=${subject}&body=${body}`);
        }

        function shareViaWeChat(url) {
            // 显示二维码或提示
            showToast('请复制链接手动分享到微信', 'info');
        }

        function shareViaDingTalk(url) {
            // 钉钉分享逻辑
            showToast('请复制链接手动分享到钉钉', 'info');
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();

            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;

            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // 自动移除
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
            return container;
        }

        // 新建文档功能 - 直接显示上传模态框
        function createNewDocument() {
            // 显示上传文档模态框
            new bootstrap.Modal(document.getElementById('uploadDocumentModal')).show();
        }

        // 原来的复杂新建文档功能（备用）
        function createNewDocumentAdvanced() {
            // 跳转到文档创建页面或显示创建模态框
            const createModal = document.createElement('div');
            createModal.className = 'modal fade';
            createModal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">新建文档</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card h-100 text-center">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <i class="fas fa-file-word fa-3x text-primary mb-3"></i>
                                            <h6>Word文档</h6>
                                            <p class="text-muted">创建新的Word文档</p>
                                            <button class="btn btn-primary" onclick="createDocument('word')">创建</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100 text-center">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                                            <h6>PDF文档</h6>
                                            <p class="text-muted">创建新的PDF文档</p>
                                            <button class="btn btn-danger" onclick="createDocument('pdf')">创建</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="card h-100 text-center">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <i class="fas fa-file-alt fa-3x text-success mb-3"></i>
                                            <h6>文本文档</h6>
                                            <p class="text-muted">创建新的文本文档</p>
                                            <button class="btn btn-success" onclick="createDocument('text')">创建</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100 text-center">
                                        <div class="card-body d-flex flex-column justify-content-center">
                                            <i class="fas fa-upload fa-3x text-info mb-3"></i>
                                            <h6>上传文档</h6>
                                            <p class="text-muted">上传现有文档</p>
                                            <button class="btn btn-info" onclick="showUploadModal()">上传</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(createModal);
            const modal = new bootstrap.Modal(createModal);
            modal.show();

            createModal.addEventListener('hidden.bs.modal', () => {
                createModal.remove();
            });
        }

        function createDocument(type) {
            // 根据文档类型创建新文档
            switch (type) {
                case 'word':
                    window.open('/dms/documents/create/word', '_blank');
                    break;
                case 'pdf':
                    window.open('/dms/documents/create/pdf', '_blank');
                    break;
                case 'text':
                    window.open('/dms/documents/create/text', '_blank');
                    break;
            }
        }

        function showUploadModal() {
            bootstrap.Modal.getInstance(document.querySelector('.modal')).hide();
            setTimeout(() => {
                new bootstrap.Modal(document.getElementById('uploadDocumentModal')).show();
            }, 300);
        }

        // 批量操作功能
        function showBatchOperations() {
            const batchModal = document.createElement('div');
            batchModal.className = 'modal fade';
            batchModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">批量操作</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">选择操作</label>
                                <select class="form-select" id="batchOperation">
                                    <option value="">请选择操作</option>
                                    <option value="download">批量下载</option>
                                    <option value="delete">批量删除</option>
                                    <option value="move">批量移动</option>
                                    <option value="copy">批量复制</option>
                                    <option value="archive">批量归档</option>
                                    <option value="publish">批量发布</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">选择文档</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll">全选</label>
                                </div>
                                <div id="documentCheckboxes" class="mt-2">
                                    <!-- 文档复选框将在这里生成 -->
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="executeBatchOperation()">执行操作</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(batchModal);
            const modal = new bootstrap.Modal(batchModal);
            modal.show();

            batchModal.addEventListener('hidden.bs.modal', () => {
                batchModal.remove();
            });

            // 加载文档列表用于批量操作
            loadDocumentsForBatch();
        }

        function loadDocumentsForBatch() {
            // 这里应该加载当前页面的文档列表
            showToast('批量操作功能开发中', 'info');
        }

        function executeBatchOperation() {
            const operation = document.getElementById('batchOperation').value;
            if (!operation) {
                showToast('请选择操作类型', 'error');
                return;
            }
            showToast(`执行${operation}操作`, 'info');
        }

        // 导出文档功能
        function exportDocuments() {
            const exportModal = document.createElement('div');
            exportModal.className = 'modal fade';
            exportModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">导出文档</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">导出格式</label>
                                <select class="form-select" id="exportFormat">
                                    <option value="excel">Excel表格</option>
                                    <option value="pdf">PDF报告</option>
                                    <option value="csv">CSV文件</option>
                                    <option value="zip">ZIP压缩包</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">导出范围</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="exportRange" value="current" checked>
                                    <label class="form-check-label">当前页面</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="exportRange" value="all">
                                    <label class="form-check-label">所有文档</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="exportRange" value="selected">
                                    <label class="form-check-label">选中文档</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeContent">
                                    <label class="form-check-label">包含文档内容</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeMetadata" checked>
                                    <label class="form-check-label">包含元数据</label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="executeExport()">开始导出</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(exportModal);
            const modal = new bootstrap.Modal(exportModal);
            modal.show();

            exportModal.addEventListener('hidden.bs.modal', () => {
                exportModal.remove();
            });
        }

        function executeExport() {
            const format = document.getElementById('exportFormat').value;
            const range = document.querySelector('input[name="exportRange"]:checked').value;
            showToast(`开始导出${format}格式文档`, 'info');
            // 这里实现实际的导出逻辑
        }

        // 高级搜索功能
        function showAdvancedSearch() {
            showToast('高级搜索功能开发中', 'info');
        }

        // 统计报表功能
        function showDocumentStats() {
            showToast('统计报表功能开发中', 'info');
        }

        // 刷新文档列表
        function refreshDocuments() {
            loadDocuments();
            showToast('文档列表已刷新', 'success');
        }

        // 设置视图模式
        function setViewMode(mode) {
            switch (mode) {
                case 'list':
                    showToast('切换到列表视图', 'info');
                    break;
                case 'grid':
                    showToast('网格视图开发中', 'info');
                    break;
                case 'timeline':
                    showToast('时间线视图开发中', 'info');
                    break;
            }
        }

        // 辅助函数
        function formatFileSize(bytes) {
            if (!bytes) return '未知';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function getStatusBadgeClass(status) {
            switch (status) {
                case 'PUBLISHED': return 'bg-success';
                case 'DRAFT': return 'bg-secondary';
                case 'UNDER_REVIEW': return 'bg-warning';
                case 'ARCHIVED': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }
    </script>

    <!-- Document Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalTitle">Document Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-0" style="height: 80vh;">
                    <div id="previewContent" class="h-100">
                        <!-- Preview content will be loaded here -->
                    </div>
                    <div id="previewError" class="alert alert-warning m-3" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="previewErrorMessage">Preview not available for this file type.</span>
                    </div>
                    <div id="previewLoading" class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading preview...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="downloadFromPreview">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文档详情模态框 -->
    <div class="modal fade" id="documentDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="documentDetailsModalLabel">文档详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="documentDetailsContent">
                    <!-- 文档详情内容将在这里动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 文档预览模态框 -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalTitle">文档预览</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="downloadFromPreview">
                            <i class="fas fa-download me-1"></i>下载
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>关闭
                        </button>
                    </div>
                </div>
                <div class="modal-body p-0" style="height: calc(100vh - 120px);">
                    <!-- 加载状态 -->
                    <div id="previewLoading" class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status"></div>
                            <div>正在加载预览...</div>
                        </div>
                    </div>

                    <!-- 预览内容 -->
                    <div id="previewContent" class="h-100" style="display: none;"></div>

                    <!-- 错误信息 -->
                    <div id="previewError" class="d-flex justify-content-center align-items-center h-100" style="display: none;">
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                            <h5>预览失败</h5>
                            <p id="previewErrorMessage" class="text-muted"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 认证工具 -->
    <script src="/dms/js/auth.js"></script>
    <!-- UI增强工具 -->
    <script src="/dms/js/ui-enhancements.js"></script>
    <!-- 文档管理器 -->
    <script src="/dms/js/document-manager.js"></script>
    <!-- 上传调试工具 -->
    <script src="/dms/js/upload-debug.js"></script>
    <script>
        // 文档审批相关函数
        async function submitForApproval(documentId) {
            const notes = prompt('请输入提交审批的说明（可选）:');
            if (notes === null) return; // 用户取消

            try {
                console.log('=== 提交文档审批 ===');
                console.log('文档ID:', documentId);

                const response = await authUtils.secureApiCall(`/dms/api/documents/approval/${documentId}/submit`, {
                    method: 'POST',
                    body: JSON.stringify({ submissionNotes: notes || '' })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('文档已提交审批！');
                    loadDocuments(); // 刷新文档列表
                    console.log('✅ 文档提交审批成功');
                } else {
                    const result = await response.json();
                    alert('提交审批失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('❌ 提交审批异常:', error);
                alert('提交审批失败: ' + error.message);
            }
        }

        async function approveDocument(documentId) {
            const notes = prompt('请输入审批意见（可选）:');
            if (notes === null) return; // 用户取消

            try {
                console.log('=== 批准文档 ===');
                console.log('文档ID:', documentId);

                const response = await authUtils.secureApiCall(`/dms/api/documents/approval/${documentId}/approve`, {
                    method: 'POST',
                    body: JSON.stringify({ approvalNotes: notes || '' })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('文档审批成功！');
                    loadDocuments(); // 刷新文档列表
                    console.log('✅ 文档审批成功');
                } else {
                    const result = await response.json();
                    alert('审批失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('❌ 审批异常:', error);
                alert('审批失败: ' + error.message);
            }
        }

        async function rejectDocument(documentId) {
            const notes = prompt('请输入拒绝原因:');
            if (!notes || notes.trim() === '') {
                alert('请输入拒绝原因');
                return;
            }

            try {
                console.log('=== 拒绝文档 ===');
                console.log('文档ID:', documentId);

                const response = await authUtils.secureApiCall(`/dms/api/documents/approval/${documentId}/reject`, {
                    method: 'POST',
                    body: JSON.stringify({ rejectionReason: notes })
                });

                if (response.ok) {
                    const result = await response.json();
                    alert('文档已拒绝！');
                    loadDocuments(); // 刷新文档列表
                    console.log('✅ 文档拒绝成功');
                } else {
                    const result = await response.json();
                    alert('拒绝失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('❌ 拒绝异常:', error);
                alert('拒绝失败: ' + error.message);
            }
        }
    </script>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
