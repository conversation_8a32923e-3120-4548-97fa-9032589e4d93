package com.pharma.dms.exception;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import com.pharma.dms.dto.ApiResponse;
import com.pharma.dms.service.AuditService;

/**
 * 全局异常处理器
 * 统一处理应用程序中的所有异常，提供一致的错误响应格式
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @Autowired
    private AuditService auditService;

    /**
     * 处理业务逻辑异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        logger.warn("业务参数异常: {}", ex.getMessage());
        
        return ResponseEntity.badRequest()
                .body(ApiResponse.error("参数错误: " + ex.getMessage(), null));
    }

    /**
     * 处理状态异常
     */
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalStateException(
            IllegalStateException ex, WebRequest request) {
        
        logger.warn("业务状态异常: {}", ex.getMessage());
        
        return ResponseEntity.badRequest()
                .body(ApiResponse.error("操作失败: " + ex.getMessage(), null));
    }

    /**
     * 处理数据完整性违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataIntegrityViolationException(
            DataIntegrityViolationException ex, WebRequest request) {
        
        logger.error("数据完整性违反: {}", ex.getMessage());
        
        // 记录审计日志
        auditService.logSystemEvent("DATA_INTEGRITY_VIOLATION", 
                "Data integrity violation: " + ex.getMessage(), 
                com.pharma.dms.entity.AuditLog.Severity.ERROR);
        
        return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(ApiResponse.error("数据冲突，请检查输入数据", null));
    }

    /**
     * 处理验证异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<ApiResponse<Object>> handleValidationException(
            Exception ex, WebRequest request) {
        
        logger.warn("数据验证失败: {}", ex.getMessage());
        
        Map<String, String> errors = new HashMap<>();
        
        if (ex instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validEx = (MethodArgumentNotValidException) ex;
            validEx.getBindingResult().getFieldErrors().forEach(error -> 
                errors.put(error.getField(), error.getDefaultMessage())
            );
        } else if (ex instanceof BindException) {
            BindException bindEx = (BindException) ex;
            bindEx.getBindingResult().getFieldErrors().forEach(error -> 
                errors.put(error.getField(), error.getDefaultMessage())
            );
        }
        
        return ResponseEntity.badRequest()
                .body(ApiResponse.error("数据验证失败", errors));
    }

    /**
     * 处理认证异常
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadCredentialsException(
            BadCredentialsException ex, WebRequest request) {
        
        logger.warn("认证失败: {}", ex.getMessage());
        
        // 记录安全审计日志
        auditService.logSystemEvent("AUTHENTICATION_FAILED", 
                "Authentication failed: " + ex.getMessage(), 
                com.pharma.dms.entity.AuditLog.Severity.WARNING);
        
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("用户名或密码错误", null));
    }

    /**
     * 处理权限异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleAccessDeniedException(
            AccessDeniedException ex, WebRequest request) {
        
        logger.warn("权限不足: {}", ex.getMessage());
        
        // 记录安全审计日志
        auditService.logSystemEvent("ACCESS_DENIED", 
                "Access denied: " + ex.getMessage(), 
                com.pharma.dms.entity.AuditLog.Severity.WARNING);
        
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(ApiResponse.error("权限不足，无法执行此操作", null));
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ApiResponse<Object>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException ex, WebRequest request) {
        
        logger.warn("文件上传大小超限: {}", ex.getMessage());
        
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE)
                .body(ApiResponse.error("文件大小超过限制，最大允许50MB", null));
    }

    /**
     * 处理资源未找到异常
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleResourceNotFoundException(
            ResourceNotFoundException ex, WebRequest request) {
        
        logger.warn("资源未找到: {}", ex.getMessage());
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("资源未找到: " + ex.getMessage(), null));
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(
            BusinessException ex, WebRequest request) {
        
        logger.warn("业务异常: {}", ex.getMessage());
        
        return ResponseEntity.status(ex.getStatus())
                .body(ApiResponse.error(ex.getMessage(), ex.getData()));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Object>> handleRuntimeException(
            RuntimeException ex, WebRequest request) {
        
        logger.error("运行时异常: ", ex);
        
        // 记录系统错误审计日志
        auditService.logSystemEvent("RUNTIME_EXCEPTION", 
                "Runtime exception: " + ex.getMessage(), 
                com.pharma.dms.entity.AuditLog.Severity.ERROR);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统内部错误，请联系管理员", null));
    }

    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(
            Exception ex, WebRequest request) {
        
        logger.error("未处理的异常: ", ex);
        
        // 记录系统错误审计日志
        auditService.logSystemEvent("UNHANDLED_EXCEPTION", 
                "Unhandled exception: " + ex.getMessage(), 
                com.pharma.dms.entity.AuditLog.Severity.CRITICAL);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("timestamp", LocalDateTime.now());
        errorDetails.put("path", request.getDescription(false));
        errorDetails.put("error", ex.getClass().getSimpleName());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统发生未知错误，请联系技术支持", errorDetails));
    }
}
