package com.pharma.dms.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.pharma.dms.entity.AuditLog;
import com.pharma.dms.entity.Document;
import com.pharma.dms.entity.DocumentCategory;
import com.pharma.dms.entity.DocumentPermission;
import com.pharma.dms.entity.User;
import com.pharma.dms.exception.BusinessException;
import com.pharma.dms.exception.ResourceNotFoundException;
import com.pharma.dms.repository.DocumentCategoryRepository;
import com.pharma.dms.repository.DocumentPermissionRepository;
import com.pharma.dms.repository.DocumentRepository;
import com.pharma.dms.repository.DocumentTagRepository;
import com.pharma.dms.repository.UserRepository;

@Service
@Transactional
public class DocumentService {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private DocumentCategoryRepository categoryRepository;

    @Autowired
    private DocumentTagRepository tagRepository;

    @Autowired
    private DocumentPermissionRepository permissionRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuditService auditService;

    @Value("${app.file.upload-dir:uploads}")
    private String uploadDir;

    @Value("${app.file.max-size:50MB}")
    private String maxFileSize;

    // Document CRUD operations
    public List<Document> getAllDocuments() {
        return documentRepository.findAll();
    }

    @Transactional(readOnly = true)
    public Page<Document> getAllDocuments(Pageable pageable) {
        try {
            System.out.println("=== DocumentService.getAllDocuments ===");
            System.out.println("Pageable: " + pageable);

            // 使用EAGER加载，直接查询即可，无需复杂的JOIN FETCH
            Page<Document> documents = documentRepository.findAll(pageable);
            System.out.println("查询到文档数量: " + documents.getTotalElements());

            // 验证关联对象已加载（EAGER加载应该自动加载）
            documents.getContent().forEach(doc -> {
                try {
                    // 验证owner已加载
                    if (doc.getOwner() != null) {
                        String username = doc.getOwner().getUsername();
                        System.out.println("✅ 文档所有者已加载: " + username);
                    } else {
                        System.out.println("⚠️ 文档所有者为空");
                    }

                    // 验证category已加载
                    if (doc.getCategory() != null) {
                        String categoryName = doc.getCategory().getName();
                        System.out.println("✅ 文档分类已加载: " + categoryName);
                    } else {
                        System.out.println("⚠️ 文档分类为空");
                    }

                    // 验证approvedBy已加载（如果有的话）
                    if (doc.getApprovedBy() != null) {
                        String approverName = doc.getApprovedBy().getUsername();
                        System.out.println("✅ 文档审批者已加载: " + approverName);
                    }
                } catch (Exception e) {
                    System.err.println("❌ 验证关联对象失败: " + e.getMessage());
                    e.printStackTrace();
                }
            });

            System.out.println("✅ 文档列表加载完成，使用EAGER加载策略");
            return documents;
        } catch (Exception e) {
            System.err.println("❌ 获取文档列表失败: " + e.getMessage());
            e.printStackTrace();
            // 返回空页面而不是抛出异常
            return Page.empty(pageable);
        }
    }

    public Page<Document> getDocumentsByStatus(Document.DocumentStatus status, Pageable pageable) {
        return documentRepository.findByStatus(status, pageable);
    }

    public Document updateDocument(Document document) {
        return documentRepository.save(document);
    }

    public Optional<Document> getDocumentById(Long id) {
        return documentRepository.findById(id);
    }

    public List<Document> getDocumentsByOwner(User owner) {
        return documentRepository.findByOwner(owner);
    }

    public List<Document> getDocumentsByCategory(DocumentCategory category) {
        return documentRepository.findByCategory(category);
    }

    public List<Document> getCurrentVersionDocuments() {
        return documentRepository.findCurrentVersionDocuments();
    }

    // File upload and management
    public Document uploadDocument(MultipartFile file, String title, String description,
                                 Long categoryId, User owner) throws IOException {
        System.out.println("=== 文档上传开始 ===");
        System.out.println("文件名: " + file.getOriginalFilename());
        System.out.println("文件大小: " + file.getSize());
        System.out.println("上传目录: " + uploadDir);

        // Validate file
        if (file.isEmpty()) {
            System.out.println("错误: 文件为空");
            throw new RuntimeException("File is empty");
        }

        // Create upload directory if it doesn't exist
        Path uploadPath = Paths.get(uploadDir);
        System.out.println("上传路径: " + uploadPath.toAbsolutePath());

        try {
            if (!Files.exists(uploadPath)) {
                System.out.println("创建上传目录...");
                Files.createDirectories(uploadPath);
                System.out.println("上传目录创建成功");
            } else {
                System.out.println("上传目录已存在");
            }
        } catch (IOException e) {
            System.out.println("创建上传目录失败: " + e.getMessage());
            throw new RuntimeException("Failed to create upload directory", e);
        }

        // Generate unique filename
        String originalFileName = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFileName);
        String uniqueFileName = UUID.randomUUID().toString() + "." + fileExtension;
        Path filePath = uploadPath.resolve(uniqueFileName);

        System.out.println("原始文件名: " + originalFileName);
        System.out.println("文件扩展名: " + fileExtension);
        System.out.println("唯一文件名: " + uniqueFileName);
        System.out.println("完整文件路径: " + filePath.toAbsolutePath());

        String checksum;
        try {
            // Copy file to upload directory
            System.out.println("开始复制文件...");
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("文件复制成功");

            // Calculate file checksum
            System.out.println("计算文件校验和...");
            checksum = calculateChecksum(file.getBytes());
            System.out.println("校验和计算完成: " + checksum.substring(0, 16) + "...");
        } catch (IOException e) {
            System.out.println("文件操作失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to save file", e);
        }

        // Create document entity
        System.out.println("创建文档实体...");
        Document document = new Document();
        document.setTitle(title);
        document.setDescription(description);
        document.setFileName(uniqueFileName);
        document.setOriginalFileName(originalFileName);
        document.setFilePath(filePath.toString());
        document.setFileSize(file.getSize());
        document.setMimeType(file.getContentType());
        document.setFileExtension(fileExtension);
        document.setChecksum(checksum);
        document.setOwner(owner);

        // 设置必需的默认值
        document.setStatus(Document.DocumentStatus.DRAFT);
        document.setClassification(Document.DocumentClassification.INTERNAL);
        document.setVersionNumber(1);
        document.setVersionLabel("v1.0");
        document.setIsCurrentVersion(true);
        document.setDownloadCount(0L);
        document.setViewCount(0L);
        document.setIsEncrypted(false);

        // Set category if provided
        if (categoryId != null) {
            try {
                DocumentCategory category = categoryRepository.findById(categoryId)
                        .orElseThrow(() -> new RuntimeException("Category not found"));
                document.setCategory(category);
                System.out.println("文档分类设置成功: " + category.getName());
            } catch (Exception e) {
                System.out.println("设置文档分类失败: " + e.getMessage());
                // 继续处理，不设置分类
            }
        }

        System.out.println("准备保存文档到数据库...");
        Document savedDocument = documentRepository.save(document);
        System.out.println("文档保存成功，ID: " + savedDocument.getId());

        // Log audit event
        auditService.logSystemEvent("DOCUMENT_UPLOADED", 
                "Document uploaded: " + originalFileName + " by " + owner.getUsername(), 
                AuditLog.Severity.INFO);

        return savedDocument;
    }

    public Document createNewVersion(Long parentDocumentId, MultipartFile file, 
                                   String versionLabel, User owner) throws IOException {
        Document parentDocument = documentRepository.findById(parentDocumentId)
                .orElseThrow(() -> new RuntimeException("Parent document not found"));

        // Mark current version as not current
        parentDocument.setIsCurrentVersion(false);
        documentRepository.save(parentDocument);

        // Create new version
        Document newVersion = uploadDocument(file, parentDocument.getTitle(), 
                                           parentDocument.getDescription(), 
                                           parentDocument.getCategory() != null ? parentDocument.getCategory().getId() : null, 
                                           owner);

        // Set version information
        newVersion.setParentDocument(parentDocument);
        newVersion.setVersionNumber(getNextVersionNumber(parentDocument));
        newVersion.setVersionLabel(versionLabel);
        newVersion.setIsCurrentVersion(true);

        Document savedVersion = documentRepository.save(newVersion);

        // Log audit event
        auditService.logSystemEvent("DOCUMENT_VERSION_CREATED", 
                "New version created for document: " + parentDocument.getTitle(), 
                AuditLog.Severity.INFO);

        return savedVersion;
    }

    @Transactional
    public void deleteDocument(Long id, User user) {
        try {
            Document document = documentRepository.findById(id)
                    .orElseThrow(() -> new ResourceNotFoundException("Document", "id", id));

            // Check permissions
            if (!hasPermission(document, user, DocumentPermission.PermissionType.DELETE)) {
                throw new BusinessException("权限不足，无法删除此文档");
            }

            // 记录删除前的状态
            Document.DocumentStatus originalStatus = document.getStatus();
            String originalTitle = document.getTitle();

            // 实现回收站机制 - 软删除
            document.setStatus(Document.DocumentStatus.ARCHIVED);
            document.setDeletedAt(LocalDateTime.now());
            document.setDeletedBy(user);

            // 保存删除状态
            documentRepository.save(document);

            // 记录详细的审计日志
            auditService.logSystemEvent("DOCUMENT_DELETED",
                    String.format("Document '%s' (ID: %d) moved to recycle bin by %s. Original status: %s",
                            originalTitle, id, user.getUsername(), originalStatus),
                    AuditLog.Severity.WARNING);

            // 如果有关联的版本，也需要标记为删除
            markRelatedVersionsAsDeleted(document, user);

            System.out.println("✅ 文档删除成功: " + originalTitle + " (ID: " + id + ")");

        } catch (Exception e) {
            // 记录错误日志
            auditService.logSystemEvent("DOCUMENT_DELETE_ERROR",
                    "Failed to delete document ID: " + id + " by " + user.getUsername() + ". Error: " + e.getMessage(),
                    AuditLog.Severity.ERROR);

            // 重新抛出异常
            if (e instanceof BusinessException || e instanceof ResourceNotFoundException) {
                throw e;
            } else {
                throw new BusinessException("删除文档时发生系统错误", e);
            }
        }
    }

    /**
     * 标记相关版本为已删除
     */
    private void markRelatedVersionsAsDeleted(Document document, User user) {
        try {
            // 查找相关的版本文档
            List<Document> relatedVersions = documentRepository.findByParentDocumentOrderByVersionNumberDesc(document);
            for (Document version : relatedVersions) {
                version.setStatus(Document.DocumentStatus.ARCHIVED);
                version.setDeletedAt(LocalDateTime.now());
                version.setDeletedBy(user);
            }
            // 批量保存版本更新
            if (!relatedVersions.isEmpty()) {
                documentRepository.saveAll(relatedVersions);
                System.out.println("标记了 " + relatedVersions.size() + " 个相关版本为已删除");
            }
        } catch (Exception e) {
            System.err.println("警告：标记相关版本删除时出错: " + e.getMessage());
            // 不影响主删除流程
        }
    }

    /**
     * 恢复已删除的文档（从回收站恢复）
     */
    @Transactional
    public void restoreDocument(Long id, User user) {
        Document document = documentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document", "id", id));

        if (document.getStatus() != Document.DocumentStatus.ARCHIVED) {
            throw new BusinessException("文档未在回收站中，无法恢复");
        }

        // 恢复文档状态
        document.setStatus(Document.DocumentStatus.DRAFT);
        document.setDeletedAt(null);
        document.setDeletedBy(null);

        documentRepository.save(document);

        // 记录恢复日志
        auditService.logSystemEvent("DOCUMENT_RESTORED",
                "Document restored from recycle bin: " + document.getTitle() + " by " + user.getUsername(),
                AuditLog.Severity.INFO);
    }

    /**
     * 永久删除文档（物理删除）
     */
    @Transactional
    public void permanentlyDeleteDocument(Long id, User user) {
        Document document = documentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document", "id", id));

        if (document.getStatus() != Document.DocumentStatus.ARCHIVED) {
            throw new BusinessException("只能永久删除回收站中的文档");
        }

        // 删除物理文件
        try {
            if (document.getFilePath() != null) {
                Path filePath = Paths.get(uploadDir, document.getFilePath());
                Files.deleteIfExists(filePath);
            }
        } catch (IOException e) {
            System.err.println("删除物理文件失败: " + e.getMessage());
        }

        // 删除数据库记录
        documentRepository.delete(document);

        // 记录永久删除日志
        auditService.logSystemEvent("DOCUMENT_PERMANENTLY_DELETED",
                "Document permanently deleted: " + document.getTitle() + " by " + user.getUsername(),
                AuditLog.Severity.CRITICAL);
    }

    /**
     * 保存分片文件
     */
    public String saveChunk(MultipartFile chunk, String uploadId, int chunkIndex, String fileName, User user) throws IOException {
        System.out.println("=== 保存分片 ===");
        System.out.println("上传ID: " + uploadId);
        System.out.println("分片索引: " + chunkIndex);
        System.out.println("分片大小: " + chunk.getSize());

        // 创建分片存储目录
        Path chunksDir = Paths.get(uploadDir, "chunks", uploadId);
        Files.createDirectories(chunksDir);

        // 分片文件名
        String chunkFileName = String.format("chunk_%d", chunkIndex);
        Path chunkPath = chunksDir.resolve(chunkFileName);

        // 保存分片
        try (InputStream inputStream = chunk.getInputStream()) {
            Files.copy(inputStream, chunkPath, StandardCopyOption.REPLACE_EXISTING);
        }

        System.out.println("分片保存成功: " + chunkPath);
        return chunkPath.toString();
    }

    /**
     * 完成分片上传，合并所有分片
     */
    @Transactional
    public Document completeChunkedUpload(String uploadId, String fileName, String title,
                                        String description, Long categoryId, int totalChunks, User owner) throws IOException {
        System.out.println("=== 合并分片 ===");
        System.out.println("上传ID: " + uploadId);
        System.out.println("总分片数: " + totalChunks);

        // 分片目录
        Path chunksDir = Paths.get(uploadDir, "chunks", uploadId);
        if (!Files.exists(chunksDir)) {
            throw new RuntimeException("分片目录不存在: " + uploadId);
        }

        // 验证所有分片都存在
        for (int i = 0; i < totalChunks; i++) {
            Path chunkPath = chunksDir.resolve(String.format("chunk_%d", i));
            if (!Files.exists(chunkPath)) {
                throw new RuntimeException("分片缺失: chunk_" + i);
            }
        }

        // 创建最终文件
        String fileExtension = getFileExtension(fileName);
        String uniqueFileName = generateUniqueFileName(fileName);
        Path finalFilePath = Paths.get(uploadDir, uniqueFileName);

        // 合并分片
        try (FileOutputStream fos = new FileOutputStream(finalFilePath.toFile())) {
            for (int i = 0; i < totalChunks; i++) {
                Path chunkPath = chunksDir.resolve(String.format("chunk_%d", i));
                Files.copy(chunkPath, fos);
                System.out.println("合并分片: " + i);
            }
        }

        // 计算文件大小和校验和
        long fileSize = Files.size(finalFilePath);
        String checksum = calculateChecksumFromFile(finalFilePath);

        System.out.println("文件合并完成");
        System.out.println("最终文件: " + finalFilePath);
        System.out.println("文件大小: " + fileSize);

        // 创建文档记录
        Document document = new Document();
        document.setTitle(title);
        document.setDescription(description);
        document.setFileName(uniqueFileName);
        document.setOriginalFileName(fileName);
        document.setFilePath(uniqueFileName);
        document.setFileSize(fileSize);
        document.setMimeType(Files.probeContentType(finalFilePath));
        document.setChecksum(checksum);
        document.setOwner(owner);
        document.setStatus(Document.DocumentStatus.DRAFT);
        document.setVersionNumber(1);
        document.setIsCurrentVersion(true);
        document.setCreatedAt(LocalDateTime.now());
        document.setUpdatedAt(LocalDateTime.now());

        // 设置分类
        if (categoryId != null) {
            DocumentCategory category = categoryRepository.findById(categoryId).orElse(null);
            document.setCategory(category);
        }

        // 保存文档
        Document savedDocument = documentRepository.save(document);

        // 清理分片文件
        cleanupChunks(chunksDir);

        // 记录审计日志
        auditService.logSystemEvent("DOCUMENT_UPLOADED",
                "Large document uploaded via chunked upload: " + title + " by " + owner.getUsername() +
                " (Size: " + fileSize + " bytes, Chunks: " + totalChunks + ")",
                AuditLog.Severity.INFO);

        System.out.println("✅ 分片上传完成: " + title + " (ID: " + savedDocument.getId() + ")");
        return savedDocument;
    }

    /**
     * 清理分片文件
     */
    private void cleanupChunks(Path chunksDir) {
        try {
            Files.walk(chunksDir)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
            System.out.println("分片文件清理完成");
        } catch (IOException e) {
            System.err.println("清理分片文件失败: " + e.getMessage());
        }
    }

    // Document access and permissions
    public boolean hasPermission(Document document, User user, DocumentPermission.PermissionType requiredPermission) {
        // Owner has all permissions
        if (document.getOwner().getId().equals(user.getId())) {
            return true;
        }

        // Check explicit permissions
        Optional<DocumentPermission> permission = permissionRepository.findActivePermission(document.getId(), user.getId());
        if (permission.isPresent() && permission.get().isValid()) {
            return permission.get().getPermissionType().includes(requiredPermission);
        }

        // Check role-based permissions
        return hasRoleBasedPermission(user, requiredPermission);
    }

    private boolean hasRoleBasedPermission(User user, DocumentPermission.PermissionType requiredPermission) {
        return user.getRoles().stream().anyMatch(role -> {
            switch (role.getName()) {
                case ROLE_ADMIN:
                    return true;
                case ROLE_QA:
                    return requiredPermission != DocumentPermission.PermissionType.DELETE;
                case ROLE_USER:
                    return requiredPermission == DocumentPermission.PermissionType.READ;
                default:
                    return false;
            }
        });
    }

    public void grantPermission(Long documentId, Long userId, DocumentPermission.PermissionType permissionType, User grantedBy) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Check if permission already exists
        Optional<DocumentPermission> existingPermission = permissionRepository.findByDocumentAndUser(document, user);
        if (existingPermission.isPresent()) {
            DocumentPermission permission = existingPermission.get();
            permission.setPermissionType(permissionType);
            permission.setIsActive(true);
            permission.setGrantedBy(grantedBy);
            permissionRepository.save(permission);
        } else {
            DocumentPermission permission = new DocumentPermission(document, user, permissionType, grantedBy);
            permissionRepository.save(permission);
        }

        // Log audit event
        auditService.logSystemEvent("DOCUMENT_PERMISSION_GRANTED", 
                "Permission " + permissionType + " granted to " + user.getUsername() + 
                " for document: " + document.getTitle(), 
                AuditLog.Severity.INFO);
    }

    // Document statistics and tracking
    public void incrementViewCount(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        document.incrementViewCount();
        documentRepository.save(document);
    }

    public void incrementDownloadCount(Long documentId) {
        Document document = documentRepository.findById(documentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        document.incrementDownloadCount();
        documentRepository.save(document);
    }

    // Search and filtering
    public Page<Document> searchDocuments(String title, String description, String fileName,
                                        Long categoryId, Long ownerId, Document.DocumentStatus status,
                                        Document.DocumentClassification classification,
                                        Boolean isCurrentVersion, Pageable pageable) {
        return documentRepository.findDocumentsWithFilters(title, description, fileName,
                categoryId, ownerId, status, classification, isCurrentVersion, pageable);
    }

    // Version management
    public List<Document> getDocumentVersions(Long parentDocumentId) {
        Document parentDocument = documentRepository.findById(parentDocumentId)
                .orElseThrow(() -> new RuntimeException("Document not found"));
        return documentRepository.findByParentDocumentOrderByVersionNumberDesc(parentDocument);
    }

    private Integer getNextVersionNumber(Document parentDocument) {
        List<Document> versions = documentRepository.findByParentDocumentOrderByVersionNumberDesc(parentDocument);
        if (versions.isEmpty()) {
            return parentDocument.getVersionNumber() + 1;
        }
        return versions.get(0).getVersionNumber() + 1;
    }

    // Utility methods
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf('.') == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }

    private String calculateChecksum(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error calculating checksum", e);
        }
    }

    /**
     * 从文件计算校验和
     */
    private String calculateChecksumFromFile(Path filePath) throws IOException {
        try {
            byte[] fileBytes = Files.readAllBytes(filePath);
            return calculateChecksum(fileBytes);
        } catch (IOException e) {
            System.err.println("计算文件校验和失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFileName) {
        String extension = getFileExtension(originalFileName);
        String baseName = originalFileName;
        if (originalFileName.contains(".")) {
            baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
        }

        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8);

        if (extension.isEmpty()) {
            return baseName + "_" + timestamp + "_" + uuid;
        } else {
            return baseName + "_" + timestamp + "_" + uuid + "." + extension;
        }
    }

    // Statistics methods - 增强错误处理
    public long getTotalDocumentCount() {
        try {
            return documentRepository.count();
        } catch (Exception e) {
            System.err.println("获取文档总数失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getDocumentCountByStatus(Document.DocumentStatus status) {
        try {
            return documentRepository.countByStatus(status);
        } catch (Exception e) {
            System.err.println("获取文档状态统计失败: " + e.getMessage());
            return 0L;
        }
    }

    public long getPendingApprovalCount() {
        try {
            return documentRepository.countPendingApproval();
        } catch (Exception e) {
            System.err.println("获取待审批文档数失败: " + e.getMessage());
            return 0L;
        }
    }

    // 新增：获取文档统计详情
    public Map<String, Object> getDocumentStatistics() {
        Map<String, Object> stats = new HashMap<>();
        try {
            stats.put("totalDocuments", getTotalDocumentCount());
            stats.put("draftDocuments", getDocumentCountByStatus(Document.DocumentStatus.DRAFT));
            stats.put("underReviewDocuments", getDocumentCountByStatus(Document.DocumentStatus.UNDER_REVIEW));
            stats.put("approvedDocuments", getDocumentCountByStatus(Document.DocumentStatus.APPROVED));
            stats.put("publishedDocuments", getDocumentCountByStatus(Document.DocumentStatus.PUBLISHED));
            stats.put("archivedDocuments", getDocumentCountByStatus(Document.DocumentStatus.ARCHIVED));
            stats.put("pendingApproval", getPendingApprovalCount());

            // 按分类统计
            Map<String, Long> categoryStats = new HashMap<>();
            List<DocumentCategory> categories = categoryRepository.findAll();
            for (DocumentCategory category : categories) {
                long count = documentRepository.countByCategoryId(category.getId());
                categoryStats.put(category.getName(), count);
            }
            stats.put("categoryStats", categoryStats);

            return stats;
        } catch (Exception e) {
            System.err.println("获取文档统计失败: " + e.getMessage());
            return Map.of(
                "totalDocuments", 0L,
                "draftDocuments", 0L,
                "publishedDocuments", 0L,
                "pendingApproval", 0L,
                "error", e.getMessage()
            );
        }
    }

    public List<Document> getRecentDocuments(int limit) {
        return documentRepository.findRecentlyCreated(LocalDateTime.now().minusDays(7), 
                Pageable.ofSize(limit));
    }

    public List<Document> getMostDownloadedDocuments(int limit) {
        return documentRepository.findMostDownloaded(Pageable.ofSize(limit));
    }

    public Document restoreDocumentVersion(Long parentDocumentId, Long versionId, User user) {
        System.out.println("=== 恢复文档版本开始 ===");

        Document parentDocument = documentRepository.findById(parentDocumentId)
                .orElseThrow(() -> new RuntimeException("Parent document not found"));

        Document versionToRestore = documentRepository.findById(versionId)
                .orElseThrow(() -> new RuntimeException("Version document not found"));

        // 验证版本关系
        if (!versionToRestore.getParentDocument().getId().equals(parentDocumentId) &&
            !versionToRestore.getId().equals(parentDocumentId)) {
            throw new RuntimeException("Version does not belong to the specified document");
        }

        // 检查权限
        if (!hasPermission(parentDocument, user, DocumentPermission.PermissionType.WRITE)) {
            throw new RuntimeException("Insufficient permissions to restore document version");
        }

        // 将所有版本设置为非当前版本
        List<Document> allVersions = getDocumentVersions(parentDocumentId);
        for (Document version : allVersions) {
            version.setIsCurrentVersion(false);
            documentRepository.save(version);
        }

        // 设置要恢复的版本为当前版本
        versionToRestore.setIsCurrentVersion(true);
        versionToRestore.setStatus(Document.DocumentStatus.PUBLISHED);
        Document restoredVersion = documentRepository.save(versionToRestore);

        // 记录审计日志
        auditService.logSystemEvent("DOCUMENT_VERSION_RESTORED",
                "Document version " + versionToRestore.getVersionNumber() +
                " restored for: " + parentDocument.getTitle() + " by " + user.getUsername(),
                AuditLog.Severity.INFO);

        System.out.println("文档版本恢复成功: " + versionToRestore.getVersionNumber());
        return restoredVersion;
    }
}
