/**
 * 文档管理优化脚本
 * 专门处理文档上传、显示、搜索等功能的优化
 */

class DocumentManager {
    constructor() {
        this.currentPage = 0;
        this.pageSize = 10;
        this.isLoading = false;
        this.retryCount = 0;
        this.maxRetries = 3;

        this.init();
    }

    // HTML转义函数，防止XSS攻击
    escapeHtml(text) {
        if (typeof text !== 'string') return text;
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 安全地设置元素文本内容
    setTextContent(element, text) {
        if (element) {
            element.textContent = text || '';
        }
    }

    // 安全地设置元素属性
    setAttribute(element, name, value) {
        if (element && value) {
            element.setAttribute(name, this.escapeHtml(value));
        }
    }

    init() {
        console.log('🚀 文档管理器初始化...');
        this.bindEvents();
        this.loadDocuments();
        this.loadCategories();
        this.loadUsers();
    }

    bindEvents() {
        // 搜索表单事件
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.currentPage = 0;
                this.loadDocuments();
            });
        }

        // 上传表单事件
        const uploadForm = document.getElementById('uploadDocumentForm');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => this.handleUpload(e));
        }

        // 清除搜索按钮
        const clearBtn = document.getElementById('clearSearchBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearSearch());
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDocuments());
        }
    }

    async loadDocuments() {
        if (this.isLoading) {
            console.log('⏳ 正在加载中，跳过重复请求');
            return;
        }

        try {
            this.isLoading = true;
            this.showLoadingState();

            console.log('=== 加载文档列表 ===');
            console.log(`页码: ${this.currentPage}, 大小: ${this.pageSize}`);

            const searchParams = new URLSearchParams({
                page: this.currentPage,
                size: this.pageSize,
                sortBy: 'createdAt',
                sortDir: 'desc'
            });

            // 添加搜索条件
            this.addSearchParams(searchParams);

            const response = await authUtils.secureApiCall(`/dms/api/documents?${searchParams}`);

            if (response.ok) {
                const result = await response.json();
                console.log('✅ 文档加载成功:', result);
                
                this.displayDocuments(result.data.content);
                this.updatePagination(result.data);
                this.retryCount = 0; // 重置重试计数
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('❌ 加载文档失败:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    addSearchParams(searchParams) {
        const title = document.getElementById('searchTitle')?.value;
        const categoryId = document.getElementById('searchCategory')?.value;
        const status = document.getElementById('searchStatus')?.value;
        const ownerId = document.getElementById('searchOwner')?.value;

        if (title) searchParams.append('title', title);
        if (categoryId) searchParams.append('categoryId', categoryId);
        if (status) searchParams.append('status', status);
        if (ownerId) searchParams.append('ownerId', ownerId);
    }

    displayDocuments(documents) {
        console.log('=== 显示文档列表 ===');
        console.log('文档数量:', documents ? documents.length : 0);

        const tbody = document.getElementById('documentsTableBody');
        if (!tbody) {
            console.error('❌ 未找到文档表格体元素');
            return;
        }

        tbody.innerHTML = '';

        if (!documents || documents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted py-5">
                        <i class="fas fa-folder-open fa-3x mb-3 text-secondary"></i>
                        <div class="h5">暂无文档</div>
                        <div class="text-muted">点击上传按钮添加第一个文档</div>
                    </td>
                </tr>
            `;
            return;
        }

        documents.forEach((doc, index) => {
            try {
                const row = this.createDocumentRow(doc, index);
                tbody.appendChild(row);
            } catch (error) {
                console.error(`❌ 创建文档行失败 (${index + 1}):`, error);
            }
        });

        console.log('✅ 文档列表显示完成');
    }

    createDocumentRow(doc, index) {
        const row = document.createElement('tr');
        row.className = 'document-row';
        row.setAttribute('data-document-id', doc.id);

        // 安全地获取并转义文档属性
        const title = this.escapeHtml(doc.title || '未命名文档');
        const originalFileName = this.escapeHtml(doc.originalFileName || doc.fileName || '未知文件');
        const status = doc.status || 'DRAFT';
        const categoryName = this.escapeHtml(doc.categoryName || doc.category?.name || '未分类');
        const ownerUsername = this.escapeHtml(doc.ownerUsername || doc.owner?.username || '未知用户');
        const fileSize = doc.fileSize || 0;
        const versionNumber = doc.versionNumber || 1;
        const createdAt = doc.createdAt || new Date().toISOString();

        // 使用安全的DOM操作而不是innerHTML
        const titleCell = document.createElement('td');
        const titleDiv = document.createElement('div');
        titleDiv.className = 'd-flex align-items-center';

        const icon = document.createElement('i');
        icon.className = 'fas fa-file-alt me-2 text-primary';

        const contentDiv = document.createElement('div');
        const titleSpan = document.createElement('div');
        titleSpan.className = 'fw-bold text-truncate';
        titleSpan.style.maxWidth = '200px';
        titleSpan.title = title;
        titleSpan.textContent = title;

        const fileNameSpan = document.createElement('small');
        fileNameSpan.className = 'text-muted text-truncate d-block';
        fileNameSpan.style.maxWidth = '200px';
        fileNameSpan.title = originalFileName;
        fileNameSpan.textContent = originalFileName;

        contentDiv.appendChild(titleSpan);
        contentDiv.appendChild(fileNameSpan);
        titleDiv.appendChild(icon);
        titleDiv.appendChild(contentDiv);
        titleCell.appendChild(titleDiv);

        // 类别列
        const categoryCell = document.createElement('td');
        const categorySpan = document.createElement('span');
        categorySpan.className = 'text-truncate d-inline-block';
        categorySpan.style.maxWidth = '120px';
        categorySpan.title = categoryName;
        categorySpan.textContent = categoryName;
        categoryCell.appendChild(categorySpan);

        // 所有者列
        const ownerCell = document.createElement('td');
        const ownerSpan = document.createElement('span');
        ownerSpan.className = 'text-truncate d-inline-block';
        ownerSpan.style.maxWidth = '100px';
        ownerSpan.title = ownerUsername;
        ownerSpan.textContent = ownerUsername;
        ownerCell.appendChild(ownerSpan);

        // 状态列
        const statusCell = document.createElement('td');
        const statusBadge = document.createElement('span');
        statusBadge.className = `badge ${this.getStatusBadgeClass(status)}`;
        statusBadge.textContent = this.getStatusDisplayName(status);
        statusCell.appendChild(statusBadge);

        // 版本列
        const versionCell = document.createElement('td');
        const versionBadge = document.createElement('span');
        versionBadge.className = 'badge bg-secondary';
        versionBadge.textContent = `v${versionNumber}`;
        versionCell.appendChild(versionBadge);

        if (doc.isCurrentVersion) {
            const starIcon = document.createElement('i');
            starIcon.className = 'fas fa-star text-warning ms-1';
            starIcon.title = '当前版本';
            versionCell.appendChild(starIcon);
        }

        // 文件大小列
        const sizeCell = document.createElement('td');
        sizeCell.textContent = this.formatFileSize(fileSize);

        // 创建时间列
        const dateCell = document.createElement('td');
        const dateSmall = document.createElement('small');
        dateSmall.textContent = this.formatDate(createdAt);
        dateCell.appendChild(dateSmall);

        // 操作按钮列
        const actionCell = document.createElement('td');
        const btnGroup = this.createActionButtons(doc.id);
        actionCell.appendChild(btnGroup);

        // 组装行
        row.appendChild(titleCell);
        row.appendChild(categoryCell);
        row.appendChild(ownerCell);
        row.appendChild(statusCell);
        row.appendChild(versionCell);
        row.appendChild(sizeCell);
        row.appendChild(dateCell);
        row.appendChild(actionCell);

        return row;
    }

    // 创建操作按钮组
    createActionButtons(docId) {
        const btnGroup = document.createElement('div');
        btnGroup.className = 'btn-group btn-group-sm';
        btnGroup.setAttribute('role', 'group');

        const buttons = [
            { icon: 'fas fa-eye', title: '查看详情', class: 'btn-outline-primary', action: 'viewDocument' },
            { icon: 'fas fa-download', title: '下载文档', class: 'btn-outline-success', action: 'downloadDocument' },
            { icon: 'fas fa-search', title: '预览文档', class: 'btn-outline-info', action: 'previewDocument' },
            { icon: 'fas fa-history', title: '版本历史', class: 'btn-outline-warning', action: 'showVersions' },
            { icon: 'fas fa-trash', title: '删除文档', class: 'btn-outline-danger', action: 'deleteDocument' }
        ];

        buttons.forEach(btn => {
            const button = document.createElement('button');
            button.className = `btn ${btn.class}`;
            button.title = btn.title;
            button.addEventListener('click', () => this[btn.action](docId));

            const icon = document.createElement('i');
            icon.className = btn.icon;
            button.appendChild(icon);

            btnGroup.appendChild(button);
        });

        return btnGroup;
    }

    async handleUpload(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const fileInput = document.getElementById('documentFile');
        
        if (!fileInput.files[0]) {
            this.showNotification('请选择要上传的文件', 'warning');
            return;
        }

        const file = fileInput.files[0];
        
        // 文件大小检查
        if (file.size > 50 * 1024 * 1024) {
            this.showNotification('文件大小不能超过50MB', 'error');
            return;
        }

        try {
            this.showUploadProgress(true);
            
            const response = await authUtils.secureApiCall('/dms/api/documents/upload', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                this.showNotification('文档上传成功！', 'success');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('uploadDocumentModal'));
                if (modal) modal.hide();
                
                // 重置表单
                form.reset();
                
                // 延迟刷新列表
                setTimeout(() => this.refreshDocuments(), 500);
            } else {
                const errorText = await response.text();
                throw new Error(errorText);
            }
        } catch (error) {
            console.error('❌ 上传失败:', error);
            this.showNotification('上传失败: ' + error.message, 'error');
        } finally {
            this.showUploadProgress(false);
        }
    }

    // 工具方法
    getStatusBadgeClass(status) {
        const classes = {
            'DRAFT': 'bg-secondary',
            'UNDER_REVIEW': 'bg-warning',
            'APPROVED': 'bg-success',
            'PUBLISHED': 'bg-primary',
            'ARCHIVED': 'bg-dark'
        };
        return classes[status] || 'bg-secondary';
    }

    getStatusDisplayName(status) {
        const names = {
            'DRAFT': '草稿',
            'UNDER_REVIEW': '审核中',
            'APPROVED': '已批准',
            'PUBLISHED': '已发布',
            'ARCHIVED': '已归档'
        };
        return names[status] || status;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        if (!dateString) return '未知时间';
        try {
            return new Date(dateString).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return '无效日期';
        }
    }

    showNotification(message, type = 'info') {
        if (typeof UIEnhancements !== 'undefined') {
            UIEnhancements.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    showLoadingState() {
        const tbody = document.getElementById('documentsTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载文档...</div>
                    </td>
                </tr>
            `;
        }
    }

    hideLoadingState() {
        // 加载状态会被实际数据替换，这里不需要特别处理
    }

    showUploadProgress(show) {
        const submitBtn = document.querySelector('#uploadDocumentForm button[type="submit"]');
        if (submitBtn) {
            if (show) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>上传中...';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>上传文档';
            }
        }
    }

    handleLoadError(error) {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`⚠️ 重试加载文档 (${this.retryCount}/${this.maxRetries})`);
            setTimeout(() => this.loadDocuments(), 1000 * this.retryCount);
        } else {
            const tbody = document.getElementById('documentsTableBody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-danger py-4">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <div>加载失败: ${error.message}</div>
                            <button class="btn btn-outline-primary btn-sm mt-2" onclick="documentManager.refreshDocuments()">
                                <i class="fas fa-redo me-1"></i>重试
                            </button>
                        </td>
                    </tr>
                `;
            }
        }
    }

    refreshDocuments() {
        this.retryCount = 0;
        this.loadDocuments();
    }

    clearSearch() {
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.reset();
            this.currentPage = 0;
            this.loadDocuments();
        }
    }

    // 占位方法，将在后续实现
    async loadCategories() {
        // TODO: 实现分类加载
    }

    async loadUsers() {
        // TODO: 实现用户加载
    }

    updatePagination(pageData) {
        // TODO: 实现分页更新
    }

    async viewDocument(id) {
        // TODO: 实现文档查看
    }

    async downloadDocument(id) {
        try {
            console.log('=== 开始下载文档 ===');
            console.log('文档ID:', id);

            const response = await authUtils.secureApiCall(`/dms/api/documents/${id}/download`, {
                method: 'GET'
            });

            if (response.ok) {
                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let fileName = 'download';

                if (contentDisposition) {
                    const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/);
                    if (fileNameMatch) {
                        fileName = decodeURIComponent(fileNameMatch[1]);
                    } else {
                        const simpleMatch = contentDisposition.match(/filename="(.+)"/);
                        if (simpleMatch) {
                            fileName = simpleMatch[1];
                        }
                    }
                }

                // 创建下载链接
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                this.showNotification('文档下载成功！', 'success');
                console.log('✅ 文档下载完成');
            } else {
                const errorText = await response.text();
                console.error('❌ 下载失败:', errorText);
                this.showNotification('下载失败: ' + errorText, 'error');
            }
        } catch (error) {
            console.error('❌ 下载异常:', error);
            this.showNotification('下载失败: ' + error.message, 'error');
        }
    }

    async previewDocument(id) {
        // TODO: 实现文档预览
    }

    async showVersions(id) {
        // TODO: 实现版本历史
    }

    async deleteDocument(id) {
        // TODO: 实现文档删除
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.documentManager = new DocumentManager();
    console.log('✅ 文档管理器已初始化');
});
