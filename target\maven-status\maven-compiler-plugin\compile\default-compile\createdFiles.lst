com\pharma\dms\config\AIConfig$AIFeatureProperties.class
com\pharma\dms\entity\DocumentPermission.class
com\pharma\dms\entity\AuditLog.class
com\pharma\dms\entity\TrainingQuestionOption.class
com\pharma\dms\controller\TrainingCourseController.class
com\pharma\dms\entity\TrainingAssignment.class
com\pharma\dms\controller\SettingsController.class
com\pharma\dms\service\UserService.class
com\pharma\dms\controller\DocumentCategoryController.class
com\pharma\dms\service\OpenRouterAIService.class
com\pharma\dms\controller\ReportController.class
com\pharma\dms\entity\TrainingProgress.class
com\pharma\dms\repository\DocumentVersionRepository.class
com\pharma\dms\controller\DocumentVersionController.class
com\pharma\dms\security\JwtUtils.class
com\pharma\dms\entity\TrainingAnswer.class
com\pharma\dms\entity\DocumentCategory.class
com\pharma\dms\entity\User.class
com\pharma\dms\repository\TrainingRecordRepository.class
com\pharma\dms\config\AIConfig$OpenRouterProperties.class
com\pharma\dms\service\DocumentService$1.class
com\pharma\dms\entity\TrainingCourse$CourseStatus.class
com\pharma\dms\entity\Role.class
com\pharma\dms\service\OCRServiceNew$OCRConfigInfo.class
com\pharma\dms\service\OpenRouterAIService$DocumentCategorizationResult.class
com\pharma\dms\service\EmailService.class
com\pharma\dms\controller\TrainingAIController.class
com\pharma\dms\entity\TrainingModule.class
com\pharma\dms\entity\Role$RoleName.class
com\pharma\dms\service\OpenRouterAIService$ComplianceCheckResult.class
com\pharma\dms\controller\DashboardController.class
com\pharma\dms\service\AuditService.class
com\pharma\dms\controller\DocumentController.class
com\pharma\dms\dto\ApiResponse.class
com\pharma\dms\service\DocumentService.class
com\pharma\dms\config\DataInitializer.class
com\pharma\dms\service\OCRService.class
com\pharma\dms\security\AuthTokenFilter.class
com\pharma\dms\entity\TrainingModule$ContentType.class
com\pharma\dms\controller\EmailController.class
com\pharma\dms\dto\LoginRequest.class
com\pharma\dms\dto\SignupRequest.class
com\pharma\dms\entity\TrainingQuestion$QuestionType.class
com\pharma\dms\dto\MessageResponse.class
com\pharma\dms\entity\TrainingCourse.class
com\pharma\dms\service\TrainingCourseService.class
com\pharma\dms\controller\AuthController.class
com\pharma\dms\service\DocumentVersionService$VersionStatistics.class
com\pharma\dms\controller\HealthController.class
com\pharma\dms\controller\WebController.class
com\pharma\dms\repository\AuditLogRepository.class
com\pharma\dms\repository\RoleRepository.class
com\pharma\dms\entity\BaseEntity.class
com\pharma\dms\entity\TrainingAttempt.class
com\pharma\dms\config\AIConfig$OCRProperties.class
com\pharma\dms\dto\DocumentDTO.class
com\pharma\dms\service\TrainingRecordService.class
com\pharma\dms\entity\Document$DocumentStatus.class
com\pharma\dms\entity\TrainingAssignment$Priority.class
com\pharma\dms\controller\HelloWorld.class
com\pharma\dms\entity\TrainingRecord$TrainingStatus.class
com\pharma\dms\entity\TrainingAssignment$AssignmentStatus.class
com\pharma\dms\entity\DocumentVersion.class
com\pharma\dms\repository\DepartmentRepository.class
com\pharma\dms\repository\DocumentTagRepository.class
com\pharma\dms\entity\AuditLog$Severity.class
com\pharma\dms\entity\Document$DocumentClassification.class
com\pharma\dms\DmsApplication.class
com\pharma\dms\security\AuthEntryPointJwt.class
com\pharma\dms\entity\DocumentVersion$ChangeType.class
com\pharma\dms\service\OCRServiceNew.class
com\pharma\dms\service\OCRService$OCRResult.class
com\pharma\dms\service\DocumentCategoryService.class
com\pharma\dms\dto\PasswordChangeRequest.class
com\pharma\dms\controller\TrainingAssignmentController.class
com\pharma\dms\controller\TestController.class
com\pharma\dms\entity\TrainingAttempt$AttemptStatus.class
com\pharma\dms\controller\AIController.class
com\pharma\dms\security\UserPrincipal.class
com\pharma\dms\service\OCRService$OCRConfigInfo.class
com\pharma\dms\entity\TrainingCourse$CourseType.class
com\pharma\dms\security\UserDetailsServiceImpl.class
com\pharma\dms\controller\ReportsController.class
com\pharma\dms\service\OCRServiceNew$OCRResult.class
com\pharma\dms\repository\TrainingAssignmentRepository.class
com\pharma\dms\controller\DepartmentController.class
com\pharma\dms\entity\Document.class
com\pharma\dms\entity\TrainingQuestion.class
com\pharma\dms\repository\TrainingCourseRepository.class
com\pharma\dms\dto\JwtResponse.class
com\pharma\dms\entity\DocumentPermission$PermissionType.class
com\pharma\dms\controller\UserController.class
com\pharma\dms\entity\TrainingRecord.class
com\pharma\dms\repository\DocumentPermissionRepository.class
com\pharma\dms\entity\Department.class
com\pharma\dms\service\OpenRouterAIService$DocumentAnalysisResult.class
com\pharma\dms\controller\DocumentApprovalController.class
com\pharma\dms\config\WebSecurityConfig.class
com\pharma\dms\entity\DocumentVersion$VersionStatus.class
com\pharma\dms\service\DocumentVersionService.class
com\pharma\dms\repository\DocumentCategoryRepository.class
com\pharma\dms\entity\DocumentTag.class
com\pharma\dms\repository\UserRepository.class
com\pharma\dms\controller\TrainingRecordController.class
com\pharma\dms\repository\DocumentRepository.class
com\pharma\dms\dto\UserUpdateRequest.class
com\pharma\dms\config\AIConfig.class
