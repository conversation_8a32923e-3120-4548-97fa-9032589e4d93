package com.pharma.dms.controller;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pharma.dms.dto.JwtResponse;
import com.pharma.dms.dto.LoginRequest;
import com.pharma.dms.dto.MessageResponse;
import com.pharma.dms.dto.SignupRequest;
import com.pharma.dms.entity.Department;
import com.pharma.dms.entity.Role;
import com.pharma.dms.entity.User;
import com.pharma.dms.repository.DepartmentRepository;
import com.pharma.dms.repository.RoleRepository;
import com.pharma.dms.repository.UserRepository;
import com.pharma.dms.security.JwtUtils;
import com.pharma.dms.security.UserPrincipal;
import com.pharma.dms.service.AuditService;
import com.pharma.dms.service.UserService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    UserRepository userRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    DepartmentRepository departmentRepository;

    @Autowired
    PasswordEncoder encoder;

    @Autowired
    JwtUtils jwtUtils;

    @Autowired
    AuditService auditService;

    @Autowired
    UserService userService;

    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        long startTime = System.currentTimeMillis();
        String username = loginRequest.getUsername();

        try {
            System.out.println("=== 登录认证开始 ===");
            System.out.println("用户: " + username);

            // 核心认证逻辑
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, loginRequest.getPassword()));

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 快速生成JWT
            String jwt = jwtUtils.generateJwtToken(authentication);

            UserPrincipal userDetails = (UserPrincipal) authentication.getPrincipal();
            List<String> roles = userDetails.getAuthorities().stream()
                    .map(item -> item.getAuthority())
                    .collect(Collectors.toList());

            // 创建响应对象
            JwtResponse response = new JwtResponse(jwt,
                    userDetails.getId(),
                    userDetails.getUsername(),
                    userDetails.getEmail(),
                    userDetails.getFirstName(),
                    userDetails.getLastName(),
                    roles);

            // 异步执行非关键操作，不阻塞响应
            CompletableFuture.runAsync(() -> {
                try {
                    userService.updateLastLogin(username);
                    auditService.logLogin(username, true);
                    System.out.println("✅ 异步登录日志记录完成: " + username);
                } catch (Exception e) {
                    System.err.println("❌ 异步登录日志记录失败: " + e.getMessage());
                }
            });

            long duration = System.currentTimeMillis() - startTime;
            System.out.println("✅ 登录成功，耗时: " + duration + "ms");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            System.out.println("❌ 登录失败，耗时: " + duration + "ms，错误: " + e.getMessage());

            // 异步记录失败日志，不阻塞响应
            CompletableFuture.runAsync(() -> {
                try {
                    auditService.logLogin(username, false);
                    userService.incrementFailedLoginAttempts(username);
                } catch (Exception ex) {
                    System.err.println("❌ 异步失败日志记录失败: " + ex.getMessage());
                }
            });

            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Invalid credentials"));
        }
    }



    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequest signUpRequest) {
        if (userRepository.existsByUsername(signUpRequest.getUsername())) {
            return ResponseEntity
                    .badRequest()
                    .body(new MessageResponse("Error: Username is already taken!"));
        }

        if (userRepository.existsByEmail(signUpRequest.getEmail())) {
            return ResponseEntity
                    .badRequest()
                    .body(new MessageResponse("Error: Email is already in use!"));
        }

        // Create new user's account
        User user = new User(signUpRequest.getUsername(),
                signUpRequest.getEmail(),
                encoder.encode(signUpRequest.getPassword()),
                signUpRequest.getFirstName(),
                signUpRequest.getLastName());

        user.setPhone(signUpRequest.getPhone());

        // Set department if provided
        if (signUpRequest.getDepartmentId() != null) {
            Department department = departmentRepository.findById(signUpRequest.getDepartmentId())
                    .orElseThrow(() -> new RuntimeException("Error: Department not found."));
            user.setDepartment(department);
        }

        Set<String> strRoles = signUpRequest.getRole();
        Set<Role> roles = new HashSet<>();

        if (strRoles == null) {
            Role userRole = roleRepository.findByName(Role.RoleName.ROLE_USER)
                    .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
            roles.add(userRole);
        } else {
            strRoles.forEach(role -> {
                switch (role) {
                    case "admin":
                        Role adminRole = roleRepository.findByName(Role.RoleName.ROLE_ADMIN)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(adminRole);
                        break;
                    case "qa":
                        Role qaRole = roleRepository.findByName(Role.RoleName.ROLE_QA)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(qaRole);
                        break;
                    default:
                        Role userRole = roleRepository.findByName(Role.RoleName.ROLE_USER)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(userRole);
                }
            });
        }

        user.setRoles(roles);
        userRepository.save(user);

        return ResponseEntity.ok(new MessageResponse("User registered successfully!"));
    }

    @GetMapping("/validate")
    public ResponseEntity<?> validateToken(HttpServletRequest request) {
        try {
            String headerAuth = request.getHeader("Authorization");
            System.out.println("=== JWT验证测试 ===");
            System.out.println("Authorization header: " + (headerAuth != null ? headerAuth.substring(0, Math.min(30, headerAuth.length())) + "..." : "null"));

            if (headerAuth != null && headerAuth.startsWith("Bearer ")) {
                String jwt = headerAuth.substring(7);
                System.out.println("Extracted JWT: " + jwt.substring(0, Math.min(30, jwt.length())) + "...");
                System.out.println("JWT length: " + jwt.length());

                if (jwtUtils.validateJwtToken(jwt)) {
                    String username = jwtUtils.getUserNameFromJwtToken(jwt);
                    System.out.println("Token valid for user: " + username);

                    // 获取用户角色信息
                    try {
                        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                        if (auth != null && auth.getPrincipal() instanceof UserPrincipal) {
                            UserPrincipal userPrincipal = (UserPrincipal) auth.getPrincipal();
                            System.out.println("User roles: " + userPrincipal.getAuthorities());
                        }
                    } catch (Exception e) {
                        System.out.println("Error getting user roles: " + e.getMessage());
                    }

                    return ResponseEntity.ok(new MessageResponse("Token is valid for user: " + username));
                } else {
                    System.out.println("Token validation failed");
                }
            } else {
                System.out.println("No valid Authorization header");
            }
            return ResponseEntity.badRequest().body(new MessageResponse("Invalid token"));
        } catch (Exception e) {
            System.out.println("Token validation error: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("Token validation failed: " + e.getMessage()));
        }
    }

    @GetMapping("/test")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<?> testAuth() {
        return ResponseEntity.ok(new MessageResponse("Authentication test successful"));
    }

    @GetMapping("/debug")
    public ResponseEntity<?> debugEndpoint() {
        try {
            System.out.println("=== DEBUG端点被调用 ===");
            return ResponseEntity.ok(new MessageResponse("Debug endpoint working"));
        } catch (Exception e) {
            System.out.println("DEBUG端点异常: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(new MessageResponse("Debug endpoint error: " + e.getMessage()));
        }
    }
}
