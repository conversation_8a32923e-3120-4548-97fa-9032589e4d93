spring:
  application:
    name: pharmaceutical-dms

  profiles:
    active: h2

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        jdbc:
          batch_size: 50
          fetch_size: 50
        order_inserts: true
        order_updates: true
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.caffeine.CaffeineCacheRegionFactory
        connection:
          provider_disables_autocommit: true
        query:
          plan_cache_max_size: 2048
    open-in-view: false

  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  # Thymeleaf Configuration
  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    suffix: .html

  # Cache Configuration
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=1h

# Application Configuration
app:
  file:
    upload-dir: uploads
    max-size: 50MB
  email:
    enabled: ${EMAIL_ENABLED:false}
    from: ${EMAIL_FROM:<EMAIL>}
  base-url: ${BASE_URL:http://localhost:8081/dms}

# Server Configuration
server:
  port: 8081
  address: 0.0.0.0
  servlet:
    context-path: /dms

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: 86400000 # 24 hours

# Email Configuration (disabled for now)
# spring:
#   mail:
#     host: ${MAIL_HOST:smtp.gmail.com}
#     port: ${MAIL_PORT:587}
#     username: ${MAIL_USERNAME:<EMAIL>}
#     password: ${MAIL_PASSWORD:your-app-password}
#     properties:
#       mail:
#         smtp:
#           auth: true
#           starttls:
#             enable: true
#           connectiontimeout: 5000
#           timeout: 3000
#           writetimeout: 5000



# AI Configuration
ai:
  openrouter:
    api-key: 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
    base-url: https://openrouter.ai/api/v1
    model: deepseek/deepseek-r1-0528:free
    max-tokens: 4000
    temperature: 0.7
    timeout: 30000

  ocr:
    enabled: true
    tesseract-path: D:/ocr
    language: chi_sim+eng
    confidence-threshold: 60

  features:
    document-analysis: true
    content-extraction: true
    compliance-check: true
    auto-categorization: true

# Logging Configuration
logging:
  level:
    com.pharma.dms: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
    org.springframework.boot: INFO
    org.springframework.web: WARN

---
# PostgreSQL Profile
spring:
  config:
    activate:
      on-profile: postgresql
  datasource:
    url: *******************************************
    username: postgres
    password:
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 5000
      idle-timeout: 300000
      max-lifetime: 900000
      initialization-fail-timeout: 3000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

---
# H2 Profile (for testing)
spring:
  config:
    activate:
      on-profile: h2
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
    driver-class-name: org.h2.Driver
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
  h2:
    console:
      enabled: true
      path: /h2-console
